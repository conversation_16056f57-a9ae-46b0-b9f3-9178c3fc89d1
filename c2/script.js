document.addEventListener('DOMContentLoaded', () => {

    // Initialize new banners
    initSoundBanner();
    initPerformanceBanner();
    initHeritageTimelineBanner();

    // --- Banner 1: "Işıkları Yak" Oyunu ---
    const spotlightBanner = document.getElementById('spotlight-banner');
    if (spotlightBanner) {
        const spotlightEffect = document.getElementById('spotlight-effect');
        const bike = document.getElementById('spotlight-bike');

        spotlightBanner.addEventListener('mousemove', (e) => {
            const rect = spotlightBanner.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            spotlightEffect.style.background = `radial-gradient(circle at ${x}px ${y}px, transparent 80px, rgba(0,0,0,0.95) 160px)`;
        });

        bike.addEventListener('click', () => {
            spotlightBanner.classList.add('light-on');
            const successMessage = document.getElementById('spotlight-success-message');
            successMessage.classList.remove('hidden');
        });

        spotlightBanner.addEventListener('mouseleave', () => {
            spotlightEffect.style.background = `radial-gradient(circle at 50% 50%, transparent 80px, rgba(0,0,0,0.95) 160px)`;
        });
    }

    // --- Banner 2: Macera Parkuru Oyunu ---
    const adventureGame = {
        banner: document.getElementById('obstacle-course-banner'),
        bike: document.getElementById('draggable-adventure-bike'),
        finishLine: document.getElementById('finish-line'),
        obstacles: document.querySelectorAll('.obstacle-game-area .obstacle'),
        successMessage: document.getElementById('adventure-success-message'),
        isDragging: false,
        offsetX: 0,
        offsetY: 0,

        init() {
            if (!this.banner || !this.bike) return;
            this.bike.addEventListener('mousedown', this.startDrag.bind(this));
            document.addEventListener('mousemove', this.drag.bind(this));
            document.addEventListener('mouseup', this.stopDrag.bind(this));
        },

        startDrag(e) {
        e.preventDefault();
            this.isDragging = true;
            this.offsetX = e.clientX - this.bike.getBoundingClientRect().left;
            this.offsetY = e.clientY - this.bike.getBoundingClientRect().top;
            this.bike.style.cursor = 'grabbing';
            this.bike.style.transition = 'none';
        },

        drag(e) {
            if (!this.isDragging) return;
        e.preventDefault();

            const bannerRect = this.banner.getBoundingClientRect();
            
            let newX = e.clientX - bannerRect.left - this.offsetX;
            let newY = e.clientY - bannerRect.top - this.offsetY;

            // Banner sınırları
            if (newX < 0) newX = 0;
            if (newY < 0) newY = 0;
            if (newX + this.bike.offsetWidth > bannerRect.width) newX = bannerRect.width - this.bike.offsetWidth;
            if (newY + this.bike.offsetHeight > bannerRect.height) newY = bannerRect.height - this.bike.offsetHeight;

            // Çarpışma kontrolü
            const futureRect = { left: newX, top: newY, right: newX + this.bike.offsetWidth, bottom: newY + this.bike.offsetHeight };
            let isColliding = false;
            for (const obstacle of this.obstacles) {
                const obstacleRect = {
                    left: obstacle.offsetLeft,
                    top: obstacle.offsetTop,
                    right: obstacle.offsetLeft + obstacle.offsetWidth,
                    bottom: obstacle.offsetTop + obstacle.offsetHeight
                };
                if (this.isOverlapping(futureRect, obstacleRect)) {
                    isColliding = true;
                    break;
                }
            }

            if (!isColliding) {
                this.bike.style.left = `${newX}px`;
                this.bike.style.top = `${newY}px`;
            }
            
            // Bitiş çizgisi kontrolü
            const bikeRectForFinish = { left: this.bike.offsetLeft, top: this.bike.offsetTop, right: this.bike.offsetLeft + this.bike.offsetWidth, bottom: this.bike.offsetTop + this.bike.offsetHeight };
            const finishRect = { left: this.finishLine.offsetLeft, top: this.finishLine.offsetTop, right: this.finishLine.offsetLeft + this.finishLine.offsetWidth, bottom: this.finishLine.offsetTop + this.finishLine.offsetHeight };
            if (this.isOverlapping(bikeRectForFinish, finishRect)) {
                this.finishLine.classList.add('over');
            } else {
                this.finishLine.classList.remove('over');
            }
        },

        stopDrag() {
            if (!this.isDragging) return;
            this.isDragging = false;
            this.bike.style.cursor = 'grab';
            this.bike.style.transition = 'transform 0.2s ease';
            
            const bikeRect = {
                left: this.bike.offsetLeft,
                top: this.bike.offsetTop,
                right: this.bike.offsetLeft + this.bike.offsetWidth,
                bottom: this.bike.offsetTop + this.bike.offsetHeight
            };
            const finishRect = {
                left: this.finishLine.offsetLeft,
                top: this.finishLine.offsetTop,
                right: this.finishLine.offsetLeft + this.finishLine.offsetWidth,
                bottom: this.finishLine.offsetTop + this.finishLine.offsetHeight
            };

            if (this.isOverlapping(bikeRect, finishRect)) {
                this.banner.classList.add('game-won');
                this.successMessage.classList.remove('hidden');
            }
        },
        
        isOverlapping(rect1, rect2) {
            return !(rect1.right < rect2.left || rect1.left > rect2.right || rect1.bottom < rect2.top || rect1.top > rect2.bottom);
        }
    };

    adventureGame.init();

    // --- Banner 3: Yarış Parkuru Oyunu ---
    const raceTrackGame = {
        banner: document.getElementById('race-track-banner'),
        bikes: document.querySelectorAll('.race-bike'),
        finishLine: document.getElementById('finish-line-race'),
        successMessage: document.getElementById('race-success-message'),
        winnerText: document.getElementById('winner-text'),
        startMessage: document.getElementById('race-start-message'),
        trackWidth: 0,
        finishLinePosition: 0,
        gameEnded: false,

        init() {
            if (!this.banner) return;
            this.trackWidth = this.banner.offsetWidth;
            this.finishLinePosition = this.finishLine.offsetLeft;
            
            this.bikes.forEach(bike => {
                bike.addEventListener('click', () => this.moveBikes(bike.dataset.bike));
            });
        },

        moveBikes(clickedBike) {
            if (this.gameEnded) return;

            if (this.startMessage) {
                this.startMessage.style.opacity = '0';
                this.startMessage.style.pointerEvents = 'none';
            }

            this.bikes.forEach(bikeElement => {
                const bikeKey = bikeElement.dataset.bike;
                let moveAmount = Math.random() * 20 + 5; // AI için temel hareket

                if (bikeKey === clickedBike) {
                    moveAmount += Math.random() * 30 + 25; // Tıklanan motora ekstra güç
                }

                let currentPos = parseFloat(bikeElement.style.left) || 20;
                let newPos = currentPos + moveAmount;
                
                bikeElement.style.left = `${newPos}px`;

                if (!this.gameEnded && (newPos + bikeElement.offsetWidth >= this.finishLinePosition)) {
                    this.endGame(bikeKey);
                }
            });
        },

        endGame(winner) {
            this.gameEnded = true;

            const winnerName = winner.toUpperCase();
            this.winnerText.textContent = `${winnerName} KAZANDI!`;
            this.successMessage.classList.remove('hidden');
            this.successMessage.classList.add('visible');

            this.bikes.forEach(bike => {
                bike.style.pointerEvents = 'none'; // Oyun bitince tıklamayı devre dışı bırak
                if (bike.dataset.bike !== winner) {
                    bike.style.opacity = '0.5';
                    bike.style.filter = 'grayscale(80%)';
                } else {
                    bike.style.transform = 'scale(1.1)';
                    bike.style.zIndex = '10';
                }
            });
        }
    };

    raceTrackGame.init();
    
    // --- Banner 4: CBR Evrimi Oyunu ---
    const evolutionBanner = {
        timelinePoints: document.querySelectorAll('.timeline-point'),
        bikeImage: document.getElementById('evolution-bike-image'),
        yearText: document.getElementById('evolution-year'),
        modelText: document.getElementById('evolution-model'),
        infoContainer: document.getElementById('evolution-info'),

        data: {
            '1992': { model: 'CBR900RR FireBlade', image: 'assets/cbr900rr_1992.jpg' },
            '2004': { model: 'CBR1000RR', image: 'assets/cbr900rr_2004.jpg' },
            '2017': { model: 'CBR1000RR Fireblade', image: 'assets/cbr900rr_2017.jpg' },
            '2024': { model: 'CBR1000RR-R Fireblade SP', image: 'assets/cbr900rr_2024.jpg' }
        },

        init() {
            if (!this.bikeImage) return;

            this.timelinePoints.forEach(point => {
                point.addEventListener('click', () => this.selectYear(point.dataset.year));
            });

            // Başlangıç durumu
            this.selectYear('1992');
        },

        selectYear(year) {
            const selectedData = this.data[year];
            if (!selectedData) return;

            // Aktif sınıfını güncelle
            this.timelinePoints.forEach(p => {
                p.classList.toggle('active', p.dataset.year === year);
            });
            
            // Değişim animasyonu
            this.bikeImage.classList.add('swapping');
            this.infoContainer.classList.add('swapping');

            setTimeout(() => {
                this.yearText.textContent = year;
                this.modelText.textContent = selectedData.model;
                this.bikeImage.src = selectedData.image;

                this.bikeImage.classList.remove('swapping');
                this.infoContainer.classList.remove('swapping');
            }, 300); // CSS transition süresiyle uyumlu olmalı
        }
    };
    
    if (document.getElementById('evolution-banner')) {
        evolutionBanner.init();
    }

    // --- Banner 5: Yarış Mirası ---
    const heritageBanner = {
        points: document.querySelectorAll('.heritage-point'),
        streetBike: document.getElementById('heritage-street-bike'),
        yearText: document.getElementById('heritage-year'),
        techText: document.getElementById('heritage-tech'),
        infoBox: document.getElementById('heritage-info-box'),

        data: {
            '1992': {
                street: 'assets/yillar/1992cbr.png',
                tech: 'CBR900RR FireBlade - "Total Control" felsefesiyle 185kg ağırlığında devrim yaratan ilk süper sport motosiklet. Tadao Baba\'nın efsanevi tasarımı ile motosiklet dünyasında yeni bir çağ başlattı.'
            },
            '2004': {
                street: 'assets/yillar/2004cbr.png',
                tech: 'CBR1000RR - MotoGP şampiyonu RC211V\'den ilham alan Unit Pro-Link arka süspansiyon sistemi ve merkezi egzoz düzeni ile yeni nesil FireBlade doğdu. 998cc motor ile güç ve kontrol mükemmel dengesi.'
            },
            '2008': {
                street: 'assets/yillar/2008cbr.png',
                tech: 'CBR1000RR - Daha kompakt ve agresif tasarım, gelişmiş aerodinamik kaportalar, slipper debriyaj teknolojisi ve C-ABS kombinli fren sistemi ile pist performansı sokaklarda.'
            },
            '2014': {
                street: 'assets/yillar/2014cbr.png',
                tech: 'CBR1000RR SP - Öhlins TTX36 arka amortisör, Öhlins NIX30 ön amortisör, Brembo Monobloc frenler, elektronik kontrol sistemleri ve karbon fiber detaylarla pist odaklı performans.'
            },
            '2024': {
                street: 'assets/yillar/2025cbr.avif',
                tech: 'CBR1000RR-R SP - MotoGP\'den gelen aerodinamik kanatçıklar, 217hp güç üreten motor, Öhlins NPX süspansiyon teknolojisi ve HRC\'nin en son yarış teknolojileri ile mutlak zirve performans.'
            },
        },

        init() {
            this.points.forEach(point => {
                point.addEventListener('click', () => this.updateView(point.dataset.year));
            });
        },

        updateView(year) {
            if (!this.data[year]) return;

            // Active durumu güncelle
            this.points.forEach(p => p.classList.remove('active'));
            document.querySelector(`.heritage-point[data-year="${year}"]`).classList.add('active');

            // Bilgi ve görselleri animasyonla değiştir
            this.infoBox.style.opacity = '0';
            this.streetBike.style.opacity = '0';
            
            setTimeout(() => {
                this.yearText.textContent = year;
                this.techText.textContent = this.data[year].tech;
                this.streetBike.src = this.data[year].street;

                this.infoBox.style.opacity = '1';
                this.streetBike.style.opacity = '1';
            }, 300);
        }
    };

    if (document.getElementById('heritage-banner')) {
        heritageBanner.init();
    }

});

// Sound Banner Functions
function initSoundBanner() {
    const engineStartBtn = document.getElementById('engine-start-btn');
    const engineStopBtn = document.getElementById('engine-stop-btn');
    const revBtn = document.getElementById('rev-btn');
    const turboBtn = document.getElementById('turbo-btn');
    const rpmCounter = document.getElementById('rpm-counter');
    const soundBike = document.getElementById('sound-bike');
    const exhaustEffect = document.getElementById('exhaust-effect');
    const soundBars = document.querySelectorAll('.sound-bar');
    const volumeSlider = document.getElementById('volume-slider');
    const volumeDisplay = document.getElementById('volume-display');

    let currentRPM = 0;
    let isEngineRunning = false;
    let currentAudio = null;

    // Audio files mapping - Gerçek Honda sesleri
    const audioFiles = {
        startup: 'assets/ses/kontak.mp3',  // Kontak sesi motor çalıştırma için
        idle: 'assets/ses/kontak.mp3',     // Kontak sesi idle için de kullanılabilir
        rev: 'assets/ses/turbo.mp3',       // Turbo sesi gaz ver butonunda
        turbo: 'assets/ses/gaz.mp3'        // Gaz sesi turbo butonunda
    };
    let audioContext;
    let oscillator;
    let gainNode;

    // Play real Honda motorcycle sound with fallback to synthetic
    function playEngineSound(type) {
        // Stop current audio
        stopSound();

        // Try to play real audio file first
        if (audioFiles[type]) {
            currentAudio = new Audio();
            currentAudio.src = audioFiles[type];

            // Apply volume from slider
            const volume = (volumeSlider ? volumeSlider.value : 70) / 100;
            currentAudio.volume = volume;

            // Handle successful loading
            currentAudio.oncanplaythrough = () => {
                currentAudio.play().catch(e => {
                    console.log('Real audio failed, using synthetic sound');
                    playSyntheticSound(type);
                });
            };

            // Handle loading errors - fallback to synthetic
            currentAudio.onerror = () => {
                console.log(`Real audio file ${type} not found, using synthetic sound`);
                currentAudio = null;
                playSyntheticSound(type);
            };

            // Set loop only for startup/idle sounds
            if (type === 'startup' || type === 'idle') {
                currentAudio.loop = true;
            } else {
                currentAudio.loop = false; // Rev ve turbo sesleri loop yapmaz
            }

            currentAudio.load();
        } else {
            // Fallback to synthetic sound
            playSyntheticSound(type);
        }
    }

    // Fallback synthetic sound generation
    function playSyntheticSound(type) {
        // Initialize audio context if needed
        if (!audioContext) {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (e) {
                console.log('Web Audio API not supported');
                return;
            }
        }

        // Resume audio context if suspended
        if (audioContext.state === 'suspended') {
            audioContext.resume();
        }

        // Create oscillator and gain nodes
        oscillator = audioContext.createOscillator();
        gainNode = audioContext.createGain();

        // Connect nodes
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        let duration = 1000;

        // Configure sound based on type
        switch(type) {
            case 'startup':
                oscillator.frequency.setValueAtTime(50, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 2);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 1);
                gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 2);
                duration = 2000;
                break;

            case 'idle':
                oscillator.frequency.setValueAtTime(80, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
                duration = 3000;
                break;

            case 'rev':
                oscillator.frequency.setValueAtTime(100, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.3);
                oscillator.frequency.exponentialRampToValueAtTime(150, audioContext.currentTime + 1);
                gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + 0.3);
                gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 1);
                duration = 1500;
                break;

            case 'turbo':
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.5);
                oscillator.frequency.exponentialRampToValueAtTime(600, audioContext.currentTime + 1.5);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.25, audioContext.currentTime + 0.5);
                gainNode.gain.linearRampToValueAtTime(0.05, audioContext.currentTime + 1.5);
                duration = 2000;
                break;
        }

        oscillator.type = type === 'turbo' ? 'sine' : 'sawtooth';

        // Apply volume from slider
        const volume = (volumeSlider ? volumeSlider.value : 70) / 100;
        gainNode.gain.value *= volume;

        // Start and stop
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration / 1000);

        oscillator.onended = () => {
            oscillator = null;
            gainNode = null;
        };
    }

    // Stop current sound
    function stopSound() {
        // Stop real audio
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            currentAudio = null;
        }

        // Stop synthetic audio
        if (oscillator) {
            try {
                oscillator.stop();
                oscillator = null;
                gainNode = null;
            } catch (e) {
                // Already stopped
            }
        }
    }

    if (engineStartBtn) {
        engineStartBtn.addEventListener('click', function() {
            isEngineRunning = true;
            currentRPM = 1000;
            updateRPM();

            // Play startup sound (sürekli loop)
            playEngineSound('startup');

            // Startup sesi zaten loop olarak ayarlandı, idle'a geçmeye gerek yok
            // Motor sesi sürekli devam edecek

            // Enable other buttons
            revBtn.disabled = false;
            turboBtn.disabled = false;
            engineStopBtn.disabled = false;
            engineStartBtn.disabled = true;
            engineStartBtn.textContent = '✅ Motor Çalışıyor';

            // Start sound visualization
            startSoundVisualization();

            // Show exhaust effect
            exhaustEffect.style.opacity = '1';
        });

        revBtn.addEventListener('click', function() {
            if (isEngineRunning) {
                currentRPM = Math.min(currentRPM + 1500, 8000);
                updateRPM();

                // Play rev sound (bir kez çalar, bitince durur)
                playEngineSound('rev');

                // Bike animation
                soundBike.style.transform = 'scale(1.05) translateX(-5px)';
                setTimeout(() => {
                    soundBike.style.transform = 'scale(1) translateX(0)';
                }, 200);
            }
        });

        turboBtn.addEventListener('click', function() {
            if (isEngineRunning) {
                currentRPM = Math.min(currentRPM + 2500, 12000);
                updateRPM();

                // Play turbo sound (bir kez çalar, bitince durur)
                playEngineSound('turbo');

                // Intense bike animation
                soundBike.style.transform = 'scale(1.1) translateX(-10px)';
                soundBike.style.filter = 'drop-shadow(0 0 30px rgba(220, 20, 60, 0.8))';
                setTimeout(() => {
                    soundBike.style.transform = 'scale(1) translateX(0)';
                    soundBike.style.filter = 'drop-shadow(0 0 20px rgba(220, 20, 60, 0.3))';
                }, 300);
            }
        });

        // Engine stop button
        engineStopBtn.addEventListener('click', function() {
            isEngineRunning = false;
            currentRPM = 0;
            updateRPM();

            // Stop all sounds
            stopSound();

            // Reset buttons
            engineStartBtn.disabled = false;
            engineStartBtn.textContent = '🔥 Motoru Çalıştır';
            revBtn.disabled = true;
            turboBtn.disabled = true;
            engineStopBtn.disabled = true;

            // Hide effects
            exhaustEffect.style.opacity = '0';

            // Stop sound visualization
            soundBars.forEach(bar => {
                bar.style.opacity = '0.3';
                bar.style.height = '20px';
            });
        });

        // Volume control
        if (volumeSlider && volumeDisplay) {
            volumeSlider.addEventListener('input', function() {
                const volume = this.value;
                volumeDisplay.textContent = volume + '%';

                // Apply volume to current audio if playing
                if (currentAudio) {
                    currentAudio.volume = volume / 100;
                }
            });
        }
    }

    function updateRPM() {
        if (rpmCounter) {
            rpmCounter.textContent = currentRPM;

            // Color change based on RPM
            if (currentRPM > 8000) {
                rpmCounter.style.color = '#ff0000';
            } else if (currentRPM > 5000) {
                rpmCounter.style.color = '#ff6b00';
            } else {
                rpmCounter.style.color = '#dc143c';
            }
        }
    }

    function startSoundVisualization() {
        soundBars.forEach((bar, index) => {
            bar.style.opacity = '1';
            bar.style.animationDuration = `${0.8 + Math.random() * 0.4}s`;
        });
    }
}

// Performance Banner Functions
function initPerformanceBanner() {
    const analyzeBtn = document.getElementById('analyze-btn');
    const performanceCards = document.querySelectorAll('.performance-card');
    const performanceBike = document.getElementById('performance-bike-img');

    const specs = {
        power: { value: 217, unit: 'HP' },
        torque: { value: 113, unit: 'Nm' },
        speed: { value: 299, unit: 'km/h' },
        weight: { value: 201, unit: 'kg' }
    };

    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', function() {
            animatePerformanceData();
            this.disabled = true;
            this.textContent = '📊 Analiz Ediliyor...';

            setTimeout(() => {
                this.textContent = '✅ Analiz Tamamlandı';
            }, 3000);
        });
    }

    function animatePerformanceData() {
        performanceCards.forEach((card, index) => {
            const metric = card.dataset.metric;
            const valueElement = card.querySelector('.metric-value');
            const targetValue = specs[metric].value;

            setTimeout(() => {
                animateValue(valueElement, 0, targetValue, 2000);
                card.style.transform = 'translateY(-10px) scale(1.05)';
                card.style.background = 'rgba(255, 215, 0, 0.2)';

                setTimeout(() => {
                    card.style.transform = 'translateY(0) scale(1)';
                    card.style.background = 'rgba(255, 255, 255, 0.1)';
                }, 500);
            }, index * 300);
        });

        // Bike glow effect
        if (performanceBike) {
            performanceBike.style.filter = 'drop-shadow(0 0 40px rgba(255, 215, 0, 0.8))';
            setTimeout(() => {
                performanceBike.style.filter = 'drop-shadow(0 0 30px rgba(255, 215, 0, 0.3))';
            }, 3000);
        }
    }

    function animateValue(element, start, end, duration) {
        const startTime = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (end - start) * progress);

            element.textContent = current;

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }
}

// Heritage Timeline Banner Functions
function initHeritageTimelineBanner() {
    const timelinePoints = document.querySelectorAll('#heritage-timeline-banner .timeline-point');
    const heritageYearTitle = document.getElementById('heritage-year-title');
    const heritageDescription = document.getElementById('heritage-description');
    const heritageBikeImg = document.getElementById('heritage-bike-img');

    const heritageData = {
        1959: {
            title: '1959 - Başlangıç',
            description: 'Honda Motor Company, Soichiro Honda tarafından kuruldu ve motosiklet dünyasında devrim yarattı.',
            image: 'assets/cbr900rr_1992.jpg'
        },
        1969: {
            title: '1969 - CB750 Devrimi',
            description: 'Honda CB750, dünyanın ilk süper motosikleti olarak tarihe geçti ve endüstriyi değiştirdi.',
            image: 'assets/cbr900rr_1992.jpg'
        },
        1992: {
            title: '1992 - FireBlade Doğuşu',
            description: 'CBR900RR FireBlade ile Honda, sportif motosiklet segmentinde yeni bir çağ başlattı.',
            image: 'assets/cbr900rr_1992.jpg'
        },
        2024: {
            title: '2024 - Gelecek Burada',
            description: 'CBR1000RR-R SP ile Honda, MotoGP teknolojisini sokaklara taşıyor.',
            image: 'assets/cbr-1000rr-r.png'
        }
    };

    timelinePoints.forEach(point => {
        point.addEventListener('click', function() {
            const year = this.dataset.year;

            // Remove active class from all points
            timelinePoints.forEach(p => p.classList.remove('active'));

            // Add active class to clicked point
            this.classList.add('active');

            // Update content
            if (heritageData[year]) {
                updateHeritageContent(heritageData[year]);
            }
        });
    });

    function updateHeritageContent(data) {
        if (heritageYearTitle) {
            heritageYearTitle.textContent = data.title;
        }

        if (heritageDescription) {
            heritageDescription.textContent = data.description;
        }

        if (heritageBikeImg) {
            heritageBikeImg.style.opacity = '0';
            setTimeout(() => {
                heritageBikeImg.src = data.image;
                heritageBikeImg.style.opacity = '1';
            }, 300);
        }
    }
}
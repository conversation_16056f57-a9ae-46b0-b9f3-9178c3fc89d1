* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', 'Arial', sans-serif;
    background-color: #f0f2f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    gap: 50px;
    padding: 40px;
}

.banner-container {
    text-align: center;
    width: 970px;
}

.banner-container h2 {
    color: #333;
    margin-bottom: 15px;
    font-family: '<PERSON><PERSON>', sans-serif;
    font-size: 32px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.banner {
    width: 100%;
    height: 250px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    overflow: hidden;
    position: relative;
    border-radius: 12px;
}

/* --- Banner 1: "Işıkları Yak" Oyunu --- */
#spotlight-banner {
    background: linear-gradient(145deg, #111, #333);
    cursor: crosshair;
}

#spotlight-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, transparent 100px, rgba(0,0,0,0.95) 180px);
    z-index: 15;
    pointer-events: none; /* Mouse olaylarının arkadaki elemanlara gitmesini sağlar */
    transition: all 0.1s ease-out; /* Hafif bir gecikme hissi için */
}

.bike-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    z-index: 5;
}

#spotlight-bike {
    width: 100%;
    height: auto;
    filter: brightness(0.15) contrast(1.1); /* Başlangıçta motor sönük */
    transition: filter 0.5s ease-in-out;
    cursor: pointer;
}

#spotlight-bike:hover {
    filter: brightness(0.6) contrast(1.1) drop-shadow(0 0 15px rgba(255,255,255,0.2));
}

#spotlight-banner.light-on #spotlight-bike {
    filter: brightness(1.2) contrast(1.1) drop-shadow(0 0 20px rgba(255, 199, 53, 0.7));
}

#spotlight-banner.light-on #spotlight-effect {
    opacity: 0;
    transition: opacity 0.7s ease;
}

.banner-content-overlay {
    position: absolute;
    top: 0; left: 0;
    width: 100%;
    height: 100%;
    padding: 25px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 10;
    pointer-events: none; /* Bu katman tıklanamaz */
}

.banner-content-overlay .honda-logo {
    width: 100px;
    opacity: 0.8;
    align-self: flex-start;
}

.banner-content-overlay .text-content {
    align-self: flex-end;
    text-align: right;
    color: white;
}

.text-content h1 {
    font-family: 'Teko', sans-serif;
    font-size: 52px;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 15px rgba(0,0,0,0.5);
}

.text-content p {
    font-size: 18px;
    opacity: 0.9;
}

.banner-message {
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(0,0,0,0.8);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 25;
    text-align: center;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
}

#spotlight-banner.light-on .banner-message {
    opacity: 1;
    transform: translateY(0);
}

.banner-message h1 {
    font-family: 'Teko', sans-serif;
    font-size: 56px;
    margin-bottom: 5px;
    color: #e41e26;
    text-transform: uppercase;
    text-shadow: 0 0 20px rgba(228, 30, 38, 0.7);
}

/* --- Banner 2: Macera Parkuru Oyunu --- */
#obstacle-course-banner {
    background: linear-gradient(rgba(0,0,0,0.1), transparent), url('https://images.unsplash.com/photo-1509316785289-0c5f2b0cecb7?q=80&w=2070&auto=format&fit=crop') no-repeat center center/cover;
    position: relative;
}

.obstacle-game-area {
    position: relative;
    width: 100%;
    height: 100%;
}

.adventure-bike-draggable {
    position: absolute;
    width: 110px;
    height: auto;
    left: 20px;
    top: 100px;
    cursor: grab;
    z-index: 10;
    filter: drop-shadow(0px 5px 10px rgba(0,0,0,0.5));
    transition: transform 0.2s ease;
}

.adventure-bike-draggable:active {
    cursor: grabbing;
    transform: scale(1.05);
}

.finish-line-area {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 120px;
    border-radius: 5px;
    z-index: 5;
    background: repeating-linear-gradient(45deg, #fff, #fff 10px, #333 10px, #333 20px);
    border: 2px solid white;
    box-shadow: 0 0 15px rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.finish-line-area.over {
    box-shadow: 0 0 25px rgba(228, 30, 38, 0.8);
}

.finish-line-area span {
    transform: rotate(-90deg);
    font-family: 'Teko', sans-serif;
    font-size: 24px;
    color: #e41e26;
    font-weight: bold;
    text-shadow: 0 0 5px white;
}

.obstacle-game-area .obstacle {
    position: absolute;
    background-size: cover;
    z-index: 6;
    filter: drop-shadow(0px 3px 6px rgba(0,0,0,0.4));
}

.obstacle.rock {
    background-image: url('https://pngimg.com/d/stone_PNG13576.png');
}

#rock1 { width: 80px; height: 80px; left: 180px; top: 10px; }
#rock2 { width: 90px; height: 90px; left: 350px; top: 150px; }
#rock3 { width: 70px; height: 70px; left: 550px; top: 20px; }
#rock4 { width: 90px; height: 90px; left: 700px; top: 140px; }
#rock5 { width: 60px; height: 60px; left: 850px; top: 30px; }

.adventure-course-logo {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 60px;
    z-index: 4;
    opacity: 0.7;
}

/* Oyun kazanıldığında eklenecek animasyonlar */
#obstacle-course-banner.game-won .finish-line-area {
    animation: pulse-finish 0.6s 2;
}
#obstacle-course-banner.game-won .adventure-bike-draggable,
#obstacle-course-banner.game-won .obstacle {
    opacity: 0;
    transition: opacity 0.5s ease;
}

#obstacle-course-banner.game-won .banner-message {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

@keyframes pulse-finish {
    0%, 100% { transform: scale(1); box-shadow: 0 0 25px rgba(228, 30, 38, 0.7); }
    50% { transform: scale(1.05); box-shadow: 0 0 35px rgba(255, 100, 100, 1); }
}

/* Genel CTA ve Logo Stilleri */
.cta-button {
    background: #e41e26;
    color: white;
    border: none;
    padding: 12px 28px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(228, 30, 38, 0.5);
    background: #ff1f2b;
}

.honda-logo {
    filter: brightness(0) invert(1);
    position: absolute;
    top: 20px;
    left: 20px;
    width: 100px;
    z-index: 30;
    opacity: 0.9;
}

.hidden {
    display: none !important;
}

/* Genel Stiller ve Yardımcı Sınıflar */
.hidden {
    display: none !important;
}

.cta-button {
    display: inline-block;
    margin-top: 20px;
}

.logo-content {
    z-index: 2;
}

.adventure-logo {
    filter: brightness(0) invert(1); /* Macera banner'ında logoyu beyaz yapar */
}

/* Urban Banner Style */
#banner-urban {
    background: linear-gradient(135deg, #1a1a1d, #4e4e50);
}

#banner-urban::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://www.transparenttextures.com/patterns/carbon-fibre.png');
    opacity: 0.1;
    z-index: 1;
}

/* Adventure Banner Style */
#banner-adventure {
    background: linear-gradient(135deg, #c7a485, #7b5b4c);
}

#banner-adventure::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://www.transparenttextures.com/patterns/wood-pattern.png');
    opacity: 0.15;
    z-index: 1;
}

/* Banner Ana Container */
.banner-fullwidth {
    width: 100%;
    max-width: 1200px;
    height: 400px;
    background: linear-gradient(135deg, #008c4f 0%, #00a658 100%);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin: 0 auto;
}

/* Slide Content */
.slide-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 40px;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.5s ease-in-out;
}

.slide-content.hidden {
    opacity: 0;
    transform: translateX(100%);
    pointer-events: none;
}

.slide-content.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

.slide-content.slide-out {
    animation: slideOut 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(120px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-120px);
    }
}

/* Banner Left */
.banner-left {
    flex: 1;
    position: relative;
}

.garanti-logo-vertical {
    width: 120px;
    height: auto;
    margin-bottom: 20px;
}

.banner-left h2 {
    color: white;
    font-size: 28px;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 20px;
}

.back-btn {
    background: none;
    border: none;
    cursor: pointer;
    position: absolute;
    bottom: 0;
    left: 0;
    transition: transform 0.2s ease;
}

.back-btn:hover {
    transform: scale(1.1);
}

/* Banner Right */
.banner-right {
    flex: 1.2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Oyun Alanı */
.oyun-banner {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.oyun-alani {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    position: relative;
    min-height: 250px;
    width: 100%;
    backdrop-filter: blur(10px);
}

.oyun-mesaj {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
}

.oran-gosterim {
    color: #ffe600;
    font-size: 20px;
    margin-bottom: 25px;
    font-weight: 500;
}

/* Dolar Oyunu */
.dolarlar {
    position: relative;
    height: 120px;
    width: 100%;
}

.dolar-ikon {
    position: absolute;
    font-size: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: bounce 2s infinite;
    user-select: none;
}

.dolar-ikon:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 0 10px #ffe600);
}

.dolar-ikon.collected {
    animation: collect 0.5s ease forwards;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes collect {
    to {
        transform: scale(0) rotate(360deg);
        opacity: 0;
    }
}

/* Kazı Kazan Oyunu */
.scratch-card-container {
    position: relative;
    width: 320px;
    height: 120px;
    margin: 0 auto;
    border-radius: 18px;
    overflow: hidden;
}

.scratch-card-odul {
    background: linear-gradient(45deg, #ffe600, #ffb700);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    border-radius: 18px;
}

.scratch-canvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    border-radius: 18px;
}

.scratch-progress {
    margin-top: 15px;
    color: white;
    font-size: 14px;
}

/* Memory Card Oyunu */
.memory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 20px 0;
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
}

.memory-card {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #ffe600, #ffb700);
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.memory-card.flipped {
    background: white;
    color: #333;
}

.memory-card.matched {
    background: #008c4f;
    color: white;
    border-color: #ffe600;
}

.memory-card:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 230, 0, 0.4);
}

.memory-stats {
    color: white;
    margin-top: 15px;
    font-size: 16px;
}

/* Şans Çarkı Oyunu */
.wheel-container {
    position: relative;
    display: inline-block;
    margin: 10px 0;
    margin-top: 0;
}

.wheel-spin-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ffe600;
    color: #333;
    border: none;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.wheel-spin-btn:hover {
    background: #ffb700;
    transform: translate(-50%, -50%) scale(1.1);
}

.wheel-spin-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.wheel-result {
    color: white;
    margin-top: 15px;
    font-size: 16px;
    font-weight: bold;
}

/* Puzzle Oyunu */
.puzzle-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: flex-start;
    margin: 20px 0;
}

.puzzle-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 5px;
    width: 240px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 10px;
}

.puzzle-slot {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed #ffe600;
    border-radius: 5px;
    min-height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.puzzle-slot.filled {
    border-style: solid;
    background: #ffe600;
}

.puzzle-pieces {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: 240px;
}

.puzzle-piece {
    width: 45px;
    height: 45px;
    background: #ffe600;
    border-radius: 5px;
    cursor: grab;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: #333;
    transition: all 0.3s ease;
    user-select: none;
}

.puzzle-piece:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 230, 0, 0.4);
}

.puzzle-piece.dragging {
    transform: rotate(5deg);
    opacity: 0.8;
}

.puzzle-progress {
    color: white;
    margin-top: 15px;
    font-size: 16px;
}

/* Snake Oyunu */
.snake-container {
    position: relative;
    display: inline-block;
    margin: 20px 0;
    border: 3px solid #ffe600;
    border-radius: 10px;
    overflow: hidden;
}

.snake-start-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ffe600;
    color: #333;
    border: none;
    border-radius: 10px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.snake-start-btn:hover {
    background: #ffb700;
    transform: translate(-50%, -50%) scale(1.1);
}

.snake-score {
    color: white;
    margin-top: 15px;
    font-size: 16px;
}

/* Bubble Pop Oyunu */
.bubble-container {
    position: relative;
    height: 150px;
    width: 100%;
    margin: 20px 0;
    overflow: hidden;
}

.bubble {
    position: absolute;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;
    user-select: none;
}

.bubble:hover {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.bubble.popped {
    animation: pop 0.5s ease forwards;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pop {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.5; }
    100% { transform: scale(0); opacity: 0; }
}

.bubble-score {
    color: white;
    margin-top: 15px;
    font-size: 16px;
}

/* Form Alanı */
.form-row-flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    width: 100%;
}

.form-blok {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
    width: 100%;
    max-width: 400px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.input-group input {
    padding: 12px 15px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    color: #333;
    outline: none;
}

.input-group input:focus {
    box-shadow: 0 0 0 3px rgba(255, 230, 0, 0.3);
}

.vade-secimi {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.vade-secimi button {
    padding: 10px 20px;
    border: 2px solid white;
    background: transparent;
    color: white;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.vade-secimi button:hover,
.vade-secimi button.active {
    background: #ffe600;
    color: #333;
    border-color: #ffe600;
}

.hesapla-btn {
    padding: 12px 30px;
    background: #ffe600;
    color: #333;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hesapla-btn:hover:not(:disabled) {
    background: #ffb700;
    transform: translateY(-2px);
}

.hesapla-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.sonuc {
    margin-top: 15px;
    background: rgba(255, 230, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    color: white;
    font-size: 18px;
    border: 2px solid #ffe600;
}

.basvur-btn {
    display: inline-block;
    padding: 15px 30px;
    background: #ff6b35;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    text-align: center;
    max-width: 300px;
    width: 100%;
}

.basvur-btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

/* Banner Indicators */
.banner-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.banner-indicators span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: background 0.3s ease;
}

.banner-indicators span.active {
    background: #ffe600;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-fullwidth {
        height: auto;
        min-height: 400px;
    }
    
    .slide-content {
        flex-direction: column;
        padding: 20px;
        gap: 20px;
    }
    
    .banner-left,
    .banner-right {
        flex: none;
        width: 100%;
        text-align: center;
    }
    
    .banner-left h2 {
        font-size: 24px;
    }
    
    .oyun-alani {
        padding: 20px;
        min-height: 200px;
    }
    
    .dolarlar {
        height: 100px;
    }
    
    .dolar-ikon {
        font-size: 30px;
    }
    
    .scratch-card-container {
        width: 250px;
        height: 100px;
    }
    
    .scratch-canvas {
        width: 250px;
        height: 100px;
    }
    
    .memory-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 250px;
    }
    
    .memory-card {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .puzzle-container {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    .bubble-container {
        height: 120px;
    }
    
    .bubble {
        font-size: 16px;
    }
    
    .form-row-flex {
        gap: 15px;
    }
    
    .vade-secimi {
        flex-wrap: wrap;
    }
    
    .vade-secimi button {
        padding: 8px 15px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .banner-left h2 {
        font-size: 20px;
    }
    
    .oyun-mesaj {
        font-size: 16px;
    }
    
    .oran-gosterim {
        font-size: 18px;
    }
    
    .scratch-card-container {
        width: 200px;
        height: 80px;
    }
    
    .scratch-canvas {
        width: 200px;
        height: 80px;
    }
    
    .memory-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 180px;
    }
    
    .memory-card {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .puzzle-board,
    .puzzle-pieces {
        width: 100px;
    }
    
    .puzzle-piece {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .bubble-container {
        height: 100px;
    }
}

/* Logo */
.logo {
    text-align: center;
    margin-bottom: 40px;
}

.logo img {
    height: 80px;
    width: auto;
}

/* Navigation */
.nav-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    gap: 30px;
}

.nav-arrow {
    background: none;
    border: none;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 0;
    transition: color 0.3s ease;
}

.nav-arrow:hover {
    color: #ffd700;
}

.nav-arrow:disabled {
    color: #666;
    cursor: not-allowed;
}

/* --- Banner 3: Yarış Parkuru --- */
#race-track-banner {
    background: linear-gradient(to right, #4a4a4a, #2b2b2b);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.race-track-logo {
    position: absolute;
    top: 10px;
    left: 15px;
    width: 60px;
    z-index: 10;
    opacity: 0.9;
}

#race-track {
    width: 100%;
    height: 180px;
    position: relative;
    background-color: transparent;
}

.race-lane {
    height: 60px;
    border-bottom: 2px dashed rgba(255, 255, 255, 0.3);
    position: relative;
    background-color: transparent;
}

.race-lane:last-child {
    border-bottom: none;
}

.race-bike {
    position: absolute;
    height: 50px;
    width: auto;
    left: 20px;
    bottom: 5px;
    transition: left 0.5s ease-in-out;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.5));
    cursor: pointer;
    background-color: transparent;
}

#finish-line-race {
    position: absolute;
    right: 30px;
    top: 0;
    width: 10px;
    height: 100%;
    background: repeating-linear-gradient(white, white 10px, black 10px, black 20px);
}

#race-start-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0,0,0,0.6);
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 1px 1px 3px black;
    z-index: 20;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

#race-success-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0,0,0,0.6);
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 1px 1px 3px black;
    z-index: 20;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

#race-success-message.visible {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

#race-success-message h1 {
    color: #ffd700;
    text-shadow: 0 0 15px #ffd700;
}

.bike-selectors {
    background-color: rgba(0,0,0,0.6);
    padding: 10px;
    text-align: center;
    color: white;
}

.bike-selectors p {
    margin-bottom: 10px;
    font-size: 14px;
}

.selector-images {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.bike-selector {
    width: 80px;
    height: auto;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 8px;
    transition: all 0.2s ease;
    background-color: rgba(255,255,255,0.1);
    padding: 5px;
}

.bike-selector:hover {
    transform: scale(1.1);
    border-color: #e41e26;
    background-color: rgba(255,255,255,0.2);
}

.bike-selector.disabled {
    cursor: not-allowed;
    filter: grayscale(80%);
    opacity: 0.6;
}

/* --- Banner 4: CBR Evrimi --- */
#evolution-banner {
    background: linear-gradient(135deg, #e9e9e9, #f9f9f9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

#evolution-display {
    position: relative;
    text-align: center;
    width: 100%;
    height: 180px; /* Zaman tüneli için yer bırak */
    display: flex;
    justify-content: center;
    align-items: center;
}

#evolution-bike-image {
    max-width: 450px;
    max-height: 170px;
    filter: drop-shadow(0 10px 15px rgba(0,0,0,0.15));
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: absolute;
    opacity: 1;
}

#evolution-bike-image.swapping {
    opacity: 0;
    transform: scale(0.95);
}

#evolution-info {
    position: absolute;
    bottom: 75px;
    right: 20px;
    text-align: right;
    color: #333;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 5;
    opacity: 1;
}

#evolution-info.swapping {
    opacity: 0;
    transform: translateY(10px);
}

#evolution-info h3 {
    font-size: 48px;
    font-family: 'Teko', sans-serif;
}

#evolution-year {
    font-family: 'Teko', sans-serif;
    font-size: 48px;
    color: #e41e26;
    line-height: 1;
}

#evolution-model {
    font-size: 18px;
    margin: 0;
}

#evolution-timeline {
    width: 80%;
    height: 30px;
    position: absolute;
    bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

#evolution-timeline::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: 3px;
    background-color: #ddd;
    z-index: 1;
}

.timeline-point {
    width: 20px;
    height: 20px;
    background-color: #fff;
    border: 3px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    justify-content: center;
}

.timeline-point span {
    position: absolute;
    top: -25px;
    font-size: 14px;
    font-weight: bold;
    color: #aaa;
    transition: all 0.3s ease;
}

.timeline-point:hover {
    border-color: #e41e26;
}

.timeline-point.active {
    background-color: #e41e26;
    border-color: #e41e26;
    transform: scale(1.2);
}

.timeline-point.active span {
    color: #e41e26;
}

/* Buradan aşağısı eski vitrin banner stilleri, siliyoruz */

/* --- Banner 5: Yarış Mirası --- */
#heritage-banner {
    background: linear-gradient(135deg, #000 0%, #1a1a1a 50%, #000 100%);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    position: relative;
}

#heritage-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

#heritage-banner > * {
    position: relative;
    z-index: 2;
}

.heritage-bikes {
    display: flex;
    width: 100%;
    height: 70%;
    position: relative;
}

.heritage-bike-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: transform 0.6s ease-in-out;
}

#street-bike-side {
    background: linear-gradient(to top, #111 70%, #222 100%);
}

.heritage-bike-container img {
    max-width: 80%;
    max-height: 70%;
    object-fit: contain;
    transition: all 0.5s ease;
    filter: drop-shadow(0 5px 10px rgba(0,0,0,0.5));
}

.heritage-info {
    position: absolute;
    bottom: 45px;
    left: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.85);
    padding: 15px 20px;
    border-radius: 10px;
    border-left: 4px solid #e41e26;
    text-align: left;
    transition: opacity 0.5s ease;
    z-index: 10;
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.heritage-info h3 {
    margin: 0 0 8px 0;
    font-family: 'Teko', sans-serif;
    font-size: 28px;
    color: #e41e26;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    float: left;
    margin-right: 15px;
}

.heritage-info p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
    color: #ffffff;
    font-weight: 400;
    text-align: justify;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.heritage-timeline {
    width: 100%;
    display: flex;
    justify-content: center;
    background: rgba(0,0,0,0.8);
    padding: 15px 0;
    position: absolute;
    bottom: 0;
    backdrop-filter: blur(5px);
}

.heritage-point {
    padding: 8px 24px;
    cursor: pointer;
    font-family: 'Teko', sans-serif;
    font-weight: bold;
    font-size: 18px;
    color: #aaa;
    transition: all 0.3s ease;
    border-radius: 25px;
    margin: 0 8px;
    border: 2px solid transparent;
    background: rgba(255, 255, 255, 0.1);
    position: relative;
}

.heritage-point:hover {
    color: #FFD700;
    background: rgba(255, 215, 0, 0.2);
    border-color: #FFD700;
    transform: translateY(-2px);
}

.heritage-point.active {
    color: #e41e26;
    background: rgba(228, 30, 38, 0.3);
    border-color: #e41e26;
    box-shadow: 0 4px 15px rgba(228, 30, 38, 0.4);
    transform: translateY(-2px) scale(1.05);
}

/* Heritage Banner Responsive */
@media (max-width: 768px) {
    .heritage-info {
        bottom: 35px;
        left: 15px;
        right: 15px;
        padding: 12px 15px;
    }

    .heritage-info h3 {
        font-size: 24px;
        float: none;
        margin-right: 0;
        margin-bottom: 5px;
        text-align: center;
    }

    .heritage-info p {
        font-size: 13px;
        text-align: center;
        -webkit-line-clamp: 2;
    }

    .heritage-timeline {
        padding: 12px 0;
    }

    .heritage-point {
        padding: 6px 16px;
        margin: 0 4px;
        font-size: 16px;
    }
}

/* Sound Experience Banner */
#sound-banner {
    background: linear-gradient(135deg, #000 0%, #1a1a1a 50%, #000 100%);
    position: relative;
    overflow: hidden;
}

.sound-visualizer {
    position: absolute;
    top: 50%;
    right: 50px;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;
    align-items: end;
    height: 100px;
}

.sound-bar {
    width: 6px;
    background: linear-gradient(to top, #dc143c, #ff6b6b);
    border-radius: 3px;
    animation: soundWave 1.5s ease-in-out infinite;
    opacity: 0.3;
}

.sound-bar:nth-child(1) { animation-delay: 0s; height: 20px; }
.sound-bar:nth-child(2) { animation-delay: 0.1s; height: 40px; }
.sound-bar:nth-child(3) { animation-delay: 0.2s; height: 60px; }
.sound-bar:nth-child(4) { animation-delay: 0.3s; height: 80px; }
.sound-bar:nth-child(5) { animation-delay: 0.4s; height: 100px; }
.sound-bar:nth-child(6) { animation-delay: 0.5s; height: 70px; }
.sound-bar:nth-child(7) { animation-delay: 0.6s; height: 50px; }
.sound-bar:nth-child(8) { animation-delay: 0.7s; height: 30px; }

@keyframes soundWave {
    0%, 100% {
        transform: scaleY(0.3);
        opacity: 0.3;
    }
    50% {
        transform: scaleY(1);
        opacity: 1;
    }
}

.sound-content {
    position: absolute;
    left: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    z-index: 2;
}

.sound-logo {
    width: 60px;
    height: auto;
    margin-bottom: 15px;
}

.sound-title {
    font-size: 2em;
    font-weight: 900;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    color: #fff;
}

.sound-subtitle {
    font-size: 1em;
    color: #ccc;
    margin-bottom: 20px;
}

.sound-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.sound-btn {
    background: linear-gradient(45deg, #dc143c, #ff6b6b);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(220, 20, 60, 0.3);
}

.sound-btn:disabled {
    background: #666;
    cursor: not-allowed;
    box-shadow: none;
}

.sound-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 20, 60, 0.5);
}

.stop-btn {
    background: linear-gradient(45deg, #ff4444, #cc0000);
}

.stop-btn:hover:not(:disabled) {
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.5);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    color: white;
    font-size: 0.9em;
}

.volume-slider {
    width: 120px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    background: #FFD700;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #FFD700;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

#volume-display {
    color: #FFD700;
    font-weight: bold;
    min-width: 35px;
}

.rpm-display {
    font-size: 1.2em;
    font-weight: bold;
}

.rpm-value {
    color: #dc143c;
    font-size: 1.5em;
}

.bike-sound-container {
    position: absolute;
    right: 150px;
    top: 50%;
    transform: translateY(-50%);
}

.sound-bike {
    width: 300px;
    height: auto;
    filter: drop-shadow(0 0 20px rgba(220, 20, 60, 0.3));
    transition: all 0.3s ease;
}

.exhaust-effect {
    position: absolute;
    right: -20px;
    top: 60%;
    width: 50px;
    height: 20px;
    background: radial-gradient(ellipse, rgba(255, 100, 0, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    animation: exhaustPulse 0.5s ease-in-out infinite;
}

@keyframes exhaustPulse {
    0%, 100% {
        opacity: 0;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* Performance Banner */
#performance-banner {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    width: 240px;
    flex-shrink: 0;
}

.performance-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 12px 8px;
    text-align: center;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    transition: all 0.5s ease;
    cursor: pointer;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.performance-card:hover {
    transform: translateY(-3px) scale(1.02);
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    border-color: rgba(255, 215, 0, 0.4);
}

.performance-card:hover .metric-value {
    color: #FFF;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

.metric-icon {
    font-size: 1.2em;
    margin-bottom: 3px;
}

.metric-value {
    font-size: 1.6em;
    font-weight: bold;
    color: #FFD700;
    transition: all 0.5s ease;
    line-height: 1;
}

.metric-unit {
    font-size: 0.7em;
    color: #ddd;
    margin-top: 1px;
}

.metric-label {
    font-size: 0.75em;
    margin-top: 3px;
    color: #fff;
    font-weight: 500;
}

.performance-content {
    flex: 1;
    text-align: center;
    color: white;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.performance-logo {
    width: 50px;
    height: auto;
    margin-bottom: 10px;
}

.performance-title {
    font-size: 1.6em;
    font-weight: 900;
    margin-bottom: 6px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    line-height: 1.1;
}

.performance-subtitle {
    font-size: 0.85em;
    color: #ddd;
    margin-bottom: 15px;
    line-height: 1.2;
    max-width: 280px;
}

.analyze-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 12px rgba(255, 215, 0, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 18px rgba(255, 215, 0, 0.6);
}

.performance-bike {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 280px;
}

#performance-bike-img {
    width: 260px;
    height: auto;
    filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.3));
    transition: all 0.5s ease;
}

/* Performance Banner Responsive Adjustments */
@media (max-width: 970px) {
    #performance-banner {
        flex-direction: column;
        padding: 15px;
        height: auto;
        min-height: 250px;
    }

    .performance-grid {
        width: 100%;
        max-width: 300px;
        margin-bottom: 15px;
    }

    .performance-content {
        padding: 10px 0;
    }

    .performance-bike {
        width: 200px;
    }

    #performance-bike-img {
        width: 180px;
    }
}

/* Heritage Timeline Banner */
#heritage-timeline-banner {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #CD853F 100%);
    position: relative;
    overflow: hidden;
}

.timeline-container {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 60px;
}

.timeline-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #8B4513, #D2691E, #CD853F);
    border-radius: 2px;
    transform: translateY(-50%);
}

.timeline-point {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
}

.timeline-point[data-year="1959"] { left: 0%; }
.timeline-point[data-year="1969"] { left: 33.33%; }
.timeline-point[data-year="1992"] { left: 66.66%; }
.timeline-point[data-year="2024"] { left: 100%; }

.point-marker {
    width: 20px;
    height: 20px;
    background: white;
    border: 4px solid #8B4513;
    border-radius: 50%;
    margin: 0 auto 5px;
    transition: all 0.3s ease;
}

.timeline-point.active .point-marker {
    background: #FFD700;
    border-color: #FF6B6B;
    transform: scale(1.3);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
}

.point-year {
    font-size: 0.9em;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    margin-bottom: 2px;
}

.point-event {
    font-size: 0.7em;
    color: #f0f0f0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
    max-width: 100px;
}

.heritage-content {
    position: absolute;
    left: 40px;
    bottom: 40px;
    color: white;
}

.heritage-logo {
    width: 50px;
    height: auto;
    margin-bottom: 10px;
}

.heritage-title {
    font-size: 1.8em;
    font-weight: 900;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.heritage-subtitle {
    font-size: 0.9em;
    color: #f0f0f0;
    margin-bottom: 15px;
}

.heritage-details h3 {
    font-size: 1.2em;
    color: #FFD700;
    margin-bottom: 5px;
}

.heritage-details p {
    font-size: 0.85em;
    line-height: 1.4;
    max-width: 300px;
}

.heritage-bike-showcase {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
}

#heritage-bike-img {
    width: 250px;
    height: auto;
    filter: drop-shadow(0 0 20px rgba(139, 69, 19, 0.4));
    transition: all 0.5s ease;
}
// Honda Banner Game States
const gameState = {
    speed: {
        slide: 0,
        currentSpeed: 0,
        maxSpeed: 0,
        rpm: 0,
        isAccelerating: false,
        gameRunning: false,
        speedDecayInterval: null
    },

    memory: {
        slide: 0,
        cards: [],
        flippedCards: [],
        matches: 0,
        moves: 0,
        gameStarted: false
    }
};

// Honda Models for Memory Game - Görsel eşleştirme
const hondaModels = [
    { name: 'CBR1000RR', image: 'assets/1000ar.png' },
    { name: 'CB650R', image: 'assets/650r.png' },
    { name: 'Africa Twin', image: 'assets/image.png' },
    { name: 'Gold Wing', image: 'assets/goldh-Photoroom.png' }
];



// Sayfa yüklendiğinde çalışacak
document.addEventListener('DOMContentLoaded', function() {
    initSpeedGame();
    initMemoryGame();
    initCinematicBanner();
    initLifestyleBanner();
});

// Cinematic Banner Initialization
function initCinematicBanner() {
    const discoverBtn = document.getElementById('cinematic-discover');
    if (discoverBtn) {
        discoverBtn.addEventListener('click', function() {
            switchSlide('cinematic', 1);
        });
    }
}

// Lifestyle Banner Initialization
function initLifestyleBanner() {
    const weatherBtns = document.querySelectorAll('.weather-btn');
    const lifestyleBanner = document.getElementById('lifestyle-banner');
    const exploreBtn = document.getElementById('lifestyle-explore');

    // Weather control buttons
    weatherBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            weatherBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Remove all weather classes
            lifestyleBanner.classList.remove('sunny', 'rainy', 'night');

            // Add selected weather class
            const weather = this.dataset.weather;
            lifestyleBanner.classList.add(weather);
        });
    });

    // Explore button
    if (exploreBtn) {
        exploreBtn.addEventListener('click', function() {
            switchSlide('lifestyle', 1);
        });
    }
}



// 2. SPEED GAME - Honda Hız Testi Oyunu
function initSpeedGame() {
    const hondaCbr = document.getElementById('honda-cbr-click');
    if (!hondaCbr) {
        console.log('Honda CBR element bulunamadı!');
        return;
    }

    console.log('Speed game başlatılıyor...');

    // Reset game state
    gameState.speed.currentSpeed = 0;
    gameState.speed.maxSpeed = 0;
    gameState.speed.rpm = 0;
    gameState.speed.isAccelerating = false;
    gameState.speed.gameRunning = false;

    // Clear any existing intervals
    if (gameState.speed.speedDecayInterval) {
        clearInterval(gameState.speed.speedDecayInterval);
    }

    // Event listener ekle
    hondaCbr.addEventListener('click', accelerateByClick);
    console.log('Click event listener eklendi');

    updateSpeedDisplay();
    updateSpeedometer();
}

function accelerateByClick() {
    console.log('CBR tıklandı! Mevcut hız:', gameState.speed.currentSpeed);

    if (gameState.speed.isAccelerating) {
        console.log('Oyun zaten bitti, tıklama engellendi');
        return;
    }

    // İlk tıklamada oyunu başlat
    if (!gameState.speed.gameRunning) {
        startSpeedDecay();
        gameState.speed.gameRunning = true;
        console.log('Hız azalma sistemi başlatıldı');
    }

    // Her tıklamada hızı artır - daha az artış
    const speedIncrease = 15 + Math.random() * 10; // 15-25 km/h arası artış (daha az)
    gameState.speed.currentSpeed += speedIncrease;
    gameState.speed.rpm += 400 + Math.random() * 300; // RPM artışı (daha az)

    console.log('Hız artışı:', speedIncrease, 'Yeni hız:', gameState.speed.currentSpeed);

    // Maksimum hızı güncelle
    if (gameState.speed.currentSpeed > gameState.speed.maxSpeed) {
        gameState.speed.maxSpeed = gameState.speed.currentSpeed;
    }

    // Limit max values
    gameState.speed.currentSpeed = Math.min(220, gameState.speed.currentSpeed);
    gameState.speed.rpm = Math.min(12000, gameState.speed.rpm);

    updateSpeedDisplay();
    updateSpeedometer();

    // Motor titreşim efekti
    const hondaCbr = document.getElementById('honda-cbr-click');
    if (hondaCbr) {
        hondaCbr.style.transform = 'scale(0.95)';
        setTimeout(() => {
            hondaCbr.style.transform = 'scale(1)';
        }, 100);
    }

    // 200 km/h'ye ulaşınca oyunu bitir
    if (gameState.speed.currentSpeed >= 200) {
        console.log('200 km/h hedefine ulaşıldı!');
        gameState.speed.isAccelerating = true; // Daha fazla tıklamayı engelle

        // Hız azalma sistemini durdur
        if (gameState.speed.speedDecayInterval) {
            clearInterval(gameState.speed.speedDecayInterval);
        }

        // İbreyi 200'de durdur
        gameState.speed.currentSpeed = 200;
        updateSpeedDisplay();
        updateSpeedometer();

        // Başarı mesajı göster ve Honda sitesine yönlendir
        setTimeout(() => {
            const finalSpeedElement = document.getElementById('final-max-speed');
            if (finalSpeedElement) {
                finalSpeedElement.textContent = '200';
            }
            switchSlide('speed', 1);
        }, 1000);

        return;
    }
}

// Sürekli hız azalma fonksiyonu
function startSpeedDecay() {
    gameState.speed.speedDecayInterval = setInterval(() => {
        if (!gameState.speed.isAccelerating && gameState.speed.gameRunning) {
            // Hızı sürekli azalt (çok daha hızlı azalma)
            gameState.speed.currentSpeed *= 0.975; // Her 100ms'de %2.5 azalma (çok daha hızlı)
            gameState.speed.rpm *= 0.980; // RPM da çok daha hızlı azalır

            // Minimum değerlere ulaşınca sıfırla
            if (gameState.speed.currentSpeed < 2) {
                gameState.speed.currentSpeed = 0;
                gameState.speed.rpm = 0;
            }

            updateSpeedDisplay();
            updateSpeedometer();

            console.log('Hız azaldı:', Math.floor(gameState.speed.currentSpeed));
        }
    }, 100); // Her 100ms'de bir kontrol et
}

function updateSpeedDisplay() {
    document.getElementById('current-speed').textContent = Math.floor(gameState.speed.currentSpeed);
    document.getElementById('current-rpm').textContent = Math.floor(gameState.speed.rpm);
}

function updateSpeedometer() {
    const needle = document.getElementById('speed-needle');
    if (!needle) return;

    // Convert speed to angle (0-220 km/h = -90 to 90 degrees)
    const angle = -90 + (gameState.speed.currentSpeed / 220) * 180;
    needle.style.transform = `rotate(${angle}deg)`;
}



// 3. MEMORY GAME - Honda Model Görsel Eşleştirme Oyunu
function initMemoryGame() {
    const grid = document.getElementById('memory-grid');
    if (!grid) return;

    grid.innerHTML = '';
    gameState.memory.cards = [];
    gameState.memory.flippedCards = [];
    gameState.memory.matches = 0;
    gameState.memory.moves = 0;
    gameState.memory.gameStarted = false;

    // 4 çift kart oluştur (8 kart) - Görseller ile
    const cardPairs = [...hondaModels, ...hondaModels];
    const shuffledCards = shuffleArray(cardPairs);

    shuffledCards.forEach((modelData, index) => {
        const card = document.createElement('div');
        card.className = 'memory-card';
        card.innerHTML = '<div class="card-back">?</div>';
        card.dataset.model = modelData.name;
        card.dataset.image = modelData.image;
        card.dataset.index = index;
        card.onclick = () => handleCardClick(index);

        grid.appendChild(card);
        gameState.memory.cards.push({
            element: card,
            model: modelData.name,
            image: modelData.image,
            flipped: false,
            matched: false
        });
    });

    updateMemoryDisplay();
}

function handleCardClick(index) {
    const card = gameState.memory.cards[index];

    if (card.flipped || card.matched || gameState.memory.flippedCards.length >= 2) {
        return;
    }

    if (!gameState.memory.gameStarted) {
        gameState.memory.gameStarted = true;
    }

    // Kartı çevir - Görsel göster
    card.flipped = true;
    card.element.classList.add('flipped');
    card.element.innerHTML = `<img src="${card.image}" alt="${card.model}" class="card-image">`;
    gameState.memory.flippedCards.push(index);

    if (gameState.memory.flippedCards.length === 2) {
        gameState.memory.moves++;
        setTimeout(checkMemoryMatch, 1000);
    }

    updateMemoryDisplay();
}

function checkMemoryMatch() {
    const [first, second] = gameState.memory.flippedCards;
    const firstCard = gameState.memory.cards[first];
    const secondCard = gameState.memory.cards[second];

    if (firstCard.model === secondCard.model) {
        // Eşleşme var
        firstCard.matched = true;
        secondCard.matched = true;
        firstCard.element.classList.add('matched');
        secondCard.element.classList.add('matched');
        gameState.memory.matches++;

        if (gameState.memory.matches === 4) {
            setTimeout(() => {
                document.getElementById('final-moves').textContent = gameState.memory.moves;
                switchSlide('memory', 1);
            }, 500);
        }
    } else {
        // Eşleşme yok - Kartları geri çevir
        firstCard.flipped = false;
        secondCard.flipped = false;
        firstCard.element.classList.remove('flipped');
        secondCard.element.classList.remove('flipped');
        firstCard.element.innerHTML = '<div class="card-back">?</div>';
        secondCard.element.innerHTML = '<div class="card-back">?</div>';
    }

    gameState.memory.flippedCards = [];
    updateMemoryDisplay();
}

function updateMemoryDisplay() {
    document.getElementById('memory-matches').textContent = gameState.memory.matches + '/4';
    document.getElementById('memory-moves').textContent = gameState.memory.moves;
}

// UTILITY FUNCTIONS
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

function switchSlide(bannerType, slideIndex) {
    const slide0 = document.getElementById(bannerType + '-slide-0');
    const slide1 = document.getElementById(bannerType + '-slide-1');
    const indicator0 = document.getElementById(bannerType + '-indicator-0');
    const indicator1 = document.getElementById(bannerType + '-indicator-1');

    if (slideIndex === 1) {
        slide0.classList.add('hidden');
        slide1.classList.remove('hidden');
        indicator0.classList.remove('active');
        indicator1.classList.add('active');
    } else {
        slide1.classList.add('hidden');
        slide0.classList.remove('hidden');
        indicator1.classList.remove('active');
        indicator0.classList.add('active');
    }

    gameState[bannerType].slide = slideIndex;
}

function goBack(bannerType) {
    switchSlide(bannerType, 0);

    // Oyunu sıfırla
    switch (bannerType) {
        case 'race':
            initRaceGame();
            break;
        case 'speed':
            // Hız azalma interval'ını temizle
            if (gameState.speed.speedDecayInterval) {
                clearInterval(gameState.speed.speedDecayInterval);
            }
            initSpeedGame();
            break;

        case 'memory':
            initMemoryGame();
            break;
    }
}












* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    padding: 20px;
    min-height: 100vh;
}

/* Honda Banner Ana Container */
.honda-banner {
    width: 970px;
    height: 250px;
    background: linear-gradient(135deg, #DC143C 0%, #B91C3C 30%, #8B0000 70%, #000000 100%);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(220, 20, 60, 0.3), 0 2px 10px rgba(0, 0, 0, 0.15);
    margin: 0 auto 30px auto;
    border: 2px solid #FFD700;
    backdrop-filter: blur(5px);
}

.honda-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.honda-banner::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700, #FFA500);
    border-radius: 17px;
    z-index: -1;
    animation: borderGlow 4s ease-in-out infinite alternate;
    opacity: 0.7;
}

@keyframes borderGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Cinematic Banner Styles */
.cinematic-banner {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    position: relative;
    overflow: hidden;
    border: 2px solid #333;
}

.cinematic-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% center, transparent 20%, rgba(0,0,0,0.6) 80%);
    z-index: 1;
}

.cinematic-bike-container {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

.cinematic-bike {
    width: 320px;
    height: auto;
    filter: drop-shadow(0 0 25px rgba(220, 20, 60, 0.5));
    animation: cinematicReveal 2s ease-out forwards;
    opacity: 0;
    transform: translateX(50px) scale(0.9);
}

@keyframes cinematicReveal {
    0% {
        opacity: 0;
        transform: translateX(50px) scale(0.9);
    }
    60% {
        opacity: 0.8;
        transform: translateX(10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.light-beam {
    position: absolute;
    top: -50px;
    left: -100px;
    width: 500px;
    height: 350px;
    background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 30%, rgba(220,20,60,0.2) 50%, transparent 70%);
    transform: rotate(-15deg);
    animation: lightSweep 4s ease-in-out infinite;
}

@keyframes lightSweep {
    0%, 100% { opacity: 0; transform: rotate(-15deg) translateX(-200px); }
    50% { opacity: 1; transform: rotate(-15deg) translateX(200px); }
}

.cinematic-content {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    color: white;
    max-width: 450px;
}

.cinematic-title {
    font-size: 2.2em;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 12px;
    text-shadow: 2px 2px 6px rgba(0,0,0,0.9);
}

.cinematic-title .line-1 {
    display: block;
    color: #FFD700;
    animation: titleSlideIn 1s ease-out 0.5s both;
}

.cinematic-title .line-2 {
    display: block;
    color: #DC143C;
    animation: titleSlideIn 1s ease-out 1s both;
}

@keyframes titleSlideIn {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.cinematic-subtitle {
    font-size: 1.1em;
    color: #ccc;
    margin-bottom: 20px;
    animation: fadeInUp 1s ease-out 1.5s both;
}

.tech-specs {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    animation: fadeInUp 1s ease-out 1.8s both;
}

.spec-item {
    text-align: center;
    padding: 8px 12px;
    background: rgba(220, 20, 60, 0.15);
    border-radius: 6px;
    border: 1px solid rgba(220, 20, 60, 0.3);
    backdrop-filter: blur(5px);
    min-width: 60px;
}

.spec-value {
    display: block;
    font-size: 1.5em;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.spec-unit {
    font-size: 0.75em;
    color: #ddd;
    margin-top: 2px;
}

.discover-btn {
    background: linear-gradient(45deg, #DC143C, #FF6B6B);
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 20px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease-out 2.2s both;
    box-shadow: 0 3px 12px rgba(220, 20, 60, 0.35);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.discover-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 20, 60, 0.6);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Lifestyle Banner Styles */
.lifestyle-banner {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #2F4F4F 100%);
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(70, 130, 180, 0.3);
}

.lifestyle-scene {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.scene-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.background-layer {
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
    transition: all 1s ease;
}

.road-layer {
    background: linear-gradient(to bottom, transparent 70%, #696969 70%, #2F4F4F 100%);
    transition: all 1s ease;
}

.bike-layer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 100px;
}

.lifestyle-bike {
    width: 280px;
    height: auto;
    filter: drop-shadow(0 8px 16px rgba(0,0,0,0.4));
    transition: all 0.5s ease;
}

.weather-layer {
    pointer-events: none;
    opacity: 0;
    transition: opacity 1s ease;
}

.rain-drop {
    position: absolute;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, transparent, #4682B4);
    animation: rainFall 1s linear infinite;
}

.rain-drop:nth-child(1) {
    left: 20%;
    animation-delay: 0s;
}

.rain-drop:nth-child(2) {
    left: 50%;
    animation-delay: 0.3s;
}

.rain-drop:nth-child(3) {
    left: 80%;
    animation-delay: 0.6s;
}

@keyframes rainFall {
    0% {
        top: -20px;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 270px;
        opacity: 0;
    }
}

.lifestyle-content {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    color: white;
    max-width: 400px;
}

.lifestyle-text h2 {
    font-size: 2.2em;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.lifestyle-text p {
    font-size: 1em;
    color: #f0f0f0;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
}

.weather-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.weather-btn {
    background: rgba(255,255,255,0.15);
    color: white;
    border: 1px solid rgba(255,255,255,0.25);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.weather-btn:hover,
.weather-btn.active {
    background: rgba(220, 20, 60, 0.8);
    border-color: #DC143C;
    transform: translateY(-1px);
}

.explore-btn {
    background: linear-gradient(45deg, #32CD32, #228B22);
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 20px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 12px rgba(50, 205, 50, 0.35);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.explore-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(50, 205, 50, 0.6);
}

/* Weather States */
.lifestyle-banner.rainy .background-layer {
    background: linear-gradient(to bottom, #708090 0%, #2F4F4F 100%);
}

.lifestyle-banner.rainy .weather-layer {
    opacity: 1;
}

.lifestyle-banner.night .background-layer {
    background: linear-gradient(to bottom, #191970 0%, #000080 100%);
}

.lifestyle-banner.night .road-layer {
    background: linear-gradient(to bottom, transparent 70%, #1C1C1C 70%, #000000 100%);
}

.lifestyle-banner.night .lifestyle-bike {
    filter: drop-shadow(0 0 20px rgba(255,255,255,0.3)) drop-shadow(0 10px 20px rgba(0,0,0,0.5));
}

.condition-features {
    margin-top: 15px;
}

.condition-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.95em;
}

.condition-icon {
    font-size: 1.2em;
}

/* Honda Logo Container */
.honda-logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #DC143C 0%, #B91C3C 100%);
    padding: 8px 16px;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #FFD700;
    box-shadow: 0 2px 10px rgba(220, 20, 60, 0.25);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

.honda-logo-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: logoShine 3s infinite;
}

@keyframes logoShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.honda-logo-svg {
    width: 28px;
    height: 28px;
    filter: brightness(0) invert(1) drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    transition: all 0.3s ease;
}

.honda-text {
    color: #FFD700;
    font-weight: bold;
    font-size: 16px;
    letter-spacing: 1.5px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.6);
    font-family: 'Arial Black', Arial, sans-serif;
}

.honda-logo-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 20, 60, 0.4);
}

.honda-logo-container:hover .honda-logo-svg {
    transform: scale(1.1);
}

/* Slide Content */
.slide-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 25px;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.5s ease-in-out;
}

.slide-content.hidden {
    opacity: 0;
    transform: translateX(100%);
    pointer-events: none;
}

.slide-content.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

.slide-content.slide-out {
    animation: slideOut 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(120px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-120px);
    }
}

/* Banner Left */
.banner-left {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.banner-left h2 {
    color: white;
    font-size: 24px;
    font-weight: 800;
    line-height: 1.3;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.7), 0 0 20px rgba(255, 215, 0, 0.3);
    font-family: 'Arial Black', Arial, sans-serif;
    letter-spacing: 1px;
}

.highlight {
    color: #FFD700;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.8), 0 0 25px rgba(255, 215, 0, 0.5);
    font-weight: 900;
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #FFD700, transparent);
    animation: highlightGlow 2s ease-in-out infinite alternate;
}

@keyframes highlightGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.back-btn {
    background: #DC143C;
    color: white;
    border: 2px solid #FFD700;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    margin-bottom: 15px;
    transition: all 0.2s ease;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    transform: scale(1.1);
    background: #FFD700;
    color: #DC143C;
}

/* Banner Right - Game Area */
.banner-right {
    flex: 1.2;
    position: relative;
}

.game-area {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 25px;
    width: 100%;
    max-width: 420px;
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
}

.game-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 215, 0, 0.05) 50%, transparent 100%);
    border-radius: 16px;
    pointer-events: none;
}

.game-message {
    color: white;
    font-size: 14px;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
}

.start-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    width: 100%;
    margin: 10px 0;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.game-stats {
    display: flex;
    justify-content: space-between;
    color: white;
    font-size: 12px;
    margin-top: 10px;
}

.game-stats span {
    background: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border-radius: 15px;
}

/* Collect Game Styles */
.collect-area {
    width: 100%;
    height: 120px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 10px;
    border: 2px solid #FFD700;
}

.collect-item {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #DC143C;
    animation: bounce 2s infinite;
}

.collect-item:hover {
    transform: scale(1.2);
    box-shadow: 0 0 20px #FFD700;
}

.collect-item.collected {
    animation: collectAnim 0.5s ease forwards;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes collectAnim {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
    100% { transform: scale(0); opacity: 0; }
}

.discount-display {
    color: #FFD700;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

/* Scratch Game Styles */
.scratch-container {
    position: relative;
    width: 300px;
    height: 120px;
    margin: 15px auto;
    border-radius: 12px;
    overflow: hidden;
    border: 3px solid #FFD700;
}

.scratch-reward {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #DC143C;
    border-radius: 8px;
}

#scratch-canvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    border-radius: 8px;
}

.scratch-progress {
    color: white;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
}

/* Memory Game Styles */
.memory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin: 15px 0;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
}

.memory-card {
    width: 85px;
    height: 85px;
    background: linear-gradient(45deg, #DC143C, #B91C3C);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s ease;
    border: 2px solid #FFD700;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
    padding: 2px;
}

.memory-card:hover {
    transform: scale(1.08);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

.card-back {
    font-size: 32px;
    color: white;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.memory-card.flipped {
    background: linear-gradient(45deg, #ffffff, #f8f8f8);
    transform: scale(1.02);
    border-color: #DC143C;
}

.card-image {
    width: 95%;
    height: 95%;
    object-fit: contain;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: white !important;
    padding: 8px;
    box-sizing: border-box;
    /* PNG şeffaflığını beyaz arka plan ile kapla */
    background-color: #ffffff !important;
    background-image: none !important;
}

.card-image-hidden {
    width: 95%;
    height: 95%;
    object-fit: contain;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: white !important;
    padding: 8px;
    box-sizing: border-box;
    /* PNG şeffaflığını beyaz arka plan ile kapla */
    background-color: #ffffff !important;
    background-image: none !important;
    filter: blur(8px) brightness(0.3) contrast(0.5);
    opacity: 0.6;
}

.memory-card.matched {
    background: linear-gradient(45deg, #00C851, #007E33);
    animation: matchPulse 0.6s ease;
    box-shadow: 0 0 25px rgba(0, 200, 81, 0.5);
}

.memory-card.matched {
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    border-color: #00C851;
}

.memory-card.matched .card-image {
    filter: brightness(1.1) drop-shadow(2px 2px 6px rgba(0,200,81,0.4));
}

@keyframes matchPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Info Content Styles */
.info-content {
    color: white;
    padding: 20px;
    text-align: center;
}

.info-content h3 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.discount-result {
    margin: 20px 0;
}

.big-discount {
    font-size: 48px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.8);
    margin-bottom: 10px;
}

.reward-info, .model-list {
    margin-bottom: 20px;
}

.reward-item, .model-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.reward-icon, .part-icon {
    font-size: 20px;
    margin-right: 10px;
}

.reward-text, .model-name {
    font-weight: 600;
}

.model-type {
    color: #FFD700;
    font-size: 12px;
}

.cta-btn {
    display: block;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    color: #DC143C;
    text-decoration: none;
    padding: 18px 35px;
    border-radius: 30px;
    text-align: center;
    font-weight: 900;
    font-size: 16px;
    letter-spacing: 1px;
    transition: all 0.4s ease;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4), 0 2px 10px rgba(0, 0, 0, 0.2);
    margin-top: 25px;
    border: 2px solid rgba(220, 20, 60, 0.3);
    position: relative;
    overflow: hidden;
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.cta-btn:hover::before {
    left: 100%;
}

.cta-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5), 0 4px 15px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 50%, #FFD700 100%);
}

.cta-btn:active {
    transform: translateY(-1px) scale(1.01);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

/* Banner Indicators */
.banner-indicators {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.banner-indicators span {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.banner-indicators span.active {
    background: #FFD700;
    transform: scale(1.2);
}
/* Responsive Design */
@media (max-width: 1024px) {
    .honda-banner {
        width: 100%;
        max-width: 970px;
    }
}

@media (max-width: 768px) {
    .honda-banner {
        height: 300px;
    }

    .slide-content {
        flex-direction: column;
        padding: 15px;
    }

    .banner-left, .banner-right {
        flex: 1;
        width: 100%;
    }

    .banner-left h2 {
        font-size: 18px;
        text-align: center;
        margin-bottom: 10px;
    }

    .game-container {
        max-width: 100%;
        padding: 15px;
    }
}





/* Speed Game Styles */
.speed-game-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    margin: 15px 0;
}

.honda-cbr-clickable {
    flex: 1;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.honda-cbr-clickable:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.honda-cbr-clickable:active {
    transform: scale(0.95);
    filter: brightness(1.2);
}

.honda-cbr-clickable img {
    width: 150px;
    height: auto;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
}

.click-text {
    color: #FFD700;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.speedometer {
    width: 150px;
    height: 150px;
    border: 6px solid #FFD700;
    border-radius: 50%;
    position: relative;
    background: radial-gradient(circle, #000 0%, #333 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.speed-needle {
    position: absolute;
    width: 3px;
    height: 60px;
    background: #DC143C;
    border-radius: 2px;
    transform-origin: bottom center;
    transform: rotate(-90deg);
    transition: transform 0.5s ease;
    z-index: 5;
}

.speed-needle::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 12px;
    background: #DC143C;
    border-radius: 50%;
}

.speed-display {
    text-align: center;
    color: #FFD700;
    font-size: 20px;
    font-weight: bold;
    z-index: 10;
}

.speed-display small {
    font-size: 10px;
    display: block;
    margin-top: 3px;
}

.speed-controls {
    text-align: center;
    margin-top: 15px;
}



/* Utility Classes */
.hidden {
    display: none !important;
}
/* Skoda Interactive Banners - 970x250 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #f5f5f5;
    padding: 20px;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

h1 {
    text-align: center;
    color: #1a1a1a;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 40px;
    font-size: 16px;
}

.banner-container {
    margin-bottom: 40px;
}

.banner-container h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.banner {
    width: 970px;
    height: 250px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.banner:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* NEW BANNER: Elroq Hero Launch */
#elroq-hero-banner {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #00A651 70%, #4CAF50 100%);
    color: white;
    overflow: hidden;
    position: relative;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.electric-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 15% 25%, rgba(0,166,81,0.4) 2px, transparent 2px),
        radial-gradient(circle at 85% 75%, rgba(76,175,80,0.3) 1px, transparent 1px),
        radial-gradient(circle at 45% 15%, rgba(255,215,0,0.2) 1px, transparent 1px),
        radial-gradient(circle at 75% 45%, rgba(0,166,81,0.3) 1px, transparent 1px);
    background-size: 80px 80px, 120px 120px, 60px 60px, 100px 100px;
    animation: particleFloat 12s ease-in-out infinite;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg);
        opacity: 0.4;
    }
    25% {
        transform: translate(15px, -10px) rotate(90deg);
        opacity: 0.7;
    }
    50% {
        transform: translate(-10px, 15px) rotate(180deg);
        opacity: 0.5;
    }
    75% {
        transform: translate(20px, 5px) rotate(270deg);
        opacity: 0.8;
    }
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(0,166,81,0.1) 0%,
        transparent 30%,
        rgba(76,175,80,0.1) 70%,
        rgba(255,215,0,0.05) 100%);
    animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 25px 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 55%;
}

.hero-brand {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.hero-logo {
    height: 35px;
    width: auto;
    filter: brightness(1.2);
}

.hero-model {
    display: flex;
    flex-direction: column;
}

.model-name {
    font-size: 32px;
    font-weight: 700;
    letter-spacing: 3px;
    color: #ffffff;
    text-shadow: 0 2px 15px rgba(0,166,81,0.6);
    font-family: 'Roboto Condensed', sans-serif;
}

.model-tagline {
    font-size: 13px;
    color: #4CAF50;
    font-weight: 400;
    letter-spacing: 0.5px;
    margin-top: 4px;
    opacity: 0.9;
}

.hero-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin: 20px 0;
    width: 100%;
}

.feature-pill {
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(0,166,81,0.4);
    border-radius: 20px;
    padding: 10px 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(15px);
    justify-content: flex-start;
    width: 100%;
}

.feature-pill:hover {
    background: rgba(0,166,81,0.25);
    border-color: rgba(0,166,81,0.7);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,166,81,0.2);
}

.feature-pill.active {
    background: rgba(0,166,81,0.4);
    border-color: #4CAF50;
    box-shadow: 0 6px 20px rgba(0,166,81,0.4);
    transform: translateY(-1px);
}

.feature-icon {
    font-size: 16px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
}

.feature-text {
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.hero-cta {
    margin-top: 30px;
    align-self: flex-start;
}

.cta-button {
    background: linear-gradient(135deg, #00A651 0%, #4CAF50 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,166,81,0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,166,81,0.4);
    background: linear-gradient(135deg, #4CAF50 0%, #00A651 100%);
}

.cta-arrow {
    transition: transform 0.3s ease;
}

.cta-button:hover .cta-arrow {
    transform: translateX(3px);
}

.hero-visual {
    position: absolute;
    right: 0;
    top: 0;
    width: 45%;
    height: 100%;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.elroq-showcase {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-car {
    width: 320px;
    height: auto;
    object-fit: contain;
    filter: drop-shadow(0 10px 30px rgba(0,166,81,0.4));
    transition: all 0.5s ease;
    transform: translateY(-20px);
}

.car-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 350px;
    height: 200px;
    background: radial-gradient(ellipse, rgba(0,166,81,0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

.feature-highlight {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0,0,0,0.85);
    border: 2px solid rgba(0,166,81,0.6);
    border-radius: 12px;
    padding: 12px 16px;
    backdrop-filter: blur(15px);
    opacity: 0;
    transform: translateY(15px);
    transition: all 0.4s ease;
    max-width: 250px;
    z-index: 3;
}

.feature-highlight.show {
    opacity: 1;
    transform: translateY(0);
}

.highlight-text {
    font-size: 12px;
    color: #4CAF50;
    font-weight: 500;
    line-height: 1.4;
    text-align: center;
}

/* Banner 1: Power & Balance */
#power-balance-banner {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 50%, #00A651 100%);
    color: white;
    overflow: hidden;
}

.power-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.electric-grid {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(0,166,81,0.3) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, rgba(255,215,0,0.2) 1px, transparent 1px);
    background-size: 40px 40px, 60px 60px;
    animation: electricFlow 8s ease-in-out infinite;
}

@keyframes electricFlow {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.3; }
    50% { transform: translate(10px, -10px) scale(1.1); opacity: 0.6; }
}

.power-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.skoda-brand-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img {
    height: 35px;
    width: auto;
    filter: brightness(0) invert(1);
}

.model-name {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 32px;
    font-weight: 700;
    letter-spacing: 3px;
    color: #00A651;
}

.power-slogan h2 {
    font-size: 24px;
    font-weight: 300;
    line-height: 1.2;
    margin: 15px 0;
    color: #fff;
}

.power-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.stat-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
}

.stat-box.active {
    opacity: 1;
    transform: scale(1);
    border-color: #00A651;
    background: rgba(0, 166, 81, 0.1);
    box-shadow: 0 0 20px rgba(0, 166, 81, 0.3);
}

.stat-box:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: #FFD700;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #FFD700;
    line-height: 1;
}

.stat-label {
    font-size: 11px;
    opacity: 0.9;
    margin-top: 2px;
    text-align: center;
}

.power-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 400px;
    height: 180px;
}

.elroq-car {
    position: relative;
    width: 100%;
    height: 100%;
}

.car-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.5));
    transition: all 0.5s ease;
}

.power-indicator {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(to right, #00A651, #FFD700);
    border-radius: 2px;
    opacity: 0;
    transition: all 0.3s ease;
}

.power-indicator.active {
    opacity: 1;
    animation: powerPulse 2s ease-in-out infinite;
}

@keyframes powerPulse {
    0%, 100% { transform: translateX(-50%) scaleX(1); opacity: 1; }
    50% { transform: translateX(-50%) scaleX(1.2); opacity: 0.7; }
}

/* Banner 2: Electric Charging */
#charging-banner {
    background: linear-gradient(135deg, #0D47A1 0%, #1976D2 50%, #42A5F5 100%);
    color: white;
    overflow: hidden;
}

.charging-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.2;
}

.energy-waves {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(ellipse at 30% 50%, rgba(255,255,255,0.1) 20%, transparent 50%),
        radial-gradient(ellipse at 70% 50%, rgba(255,215,0,0.1) 20%, transparent 50%);
    background-size: 200px 100px, 150px 80px;
    animation: energyFlow 6s ease-in-out infinite;
}

@keyframes energyFlow {
    0%, 100% { transform: translateX(0) scale(1); opacity: 0.2; }
    50% { transform: translateX(20px) scale(1.1); opacity: 0.4; }
}

.charging-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.charging-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img-small {
    height: 28px;
    width: auto;
    filter: brightness(0) invert(1);
}

.charging-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.model-text {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 2px;
    color: #FFD700;
}

.electric-badge {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    color: #fff;
}

.charging-info {
    display: flex;
    align-items: center;
    gap: 30px;
    margin: 20px 0;
}

.charging-main {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.charge-time {
    font-size: 48px;
    font-weight: 700;
    color: #FFD700;
    line-height: 1;
}

.charge-desc {
    font-size: 14px;
    text-align: center;
    opacity: 0.9;
    margin-top: 5px;
}

.charging-details {
    display: flex;
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
}

.detail-number {
    font-size: 24px;
    font-weight: 700;
    color: #42A5F5;
    line-height: 1;
}

.detail-text {
    font-size: 11px;
    opacity: 0.8;
    margin-top: 2px;
    text-align: center;
}

.charging-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.charging-station {
    position: absolute;
    left: 0;
    top: 30px;
    width: 60px;
    height: 120px;
    background: linear-gradient(to bottom, #333, #555);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.charging-station::before {
    content: '⚡';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    color: #FFD700;
    animation: chargingPulse 2s ease-in-out infinite;
}

@keyframes chargingPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.elroq-charging {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 250px;
    height: 120px;
}

.charging-car {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 8px 25px rgba(66, 165, 245, 0.4));
}

.battery-container {
    position: absolute;
    top: 15px;
    right: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.battery-percentage {
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
}

.battery-indicator {
    position: relative;
    width: 50px;
    height: 25px;
    border: 2px solid #fff;
    border-radius: 6px;
    background: rgba(0,0,0,0.3);
    overflow: hidden;
}

.battery-level {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, #4CAF50 0%, #66BB6A 50%, #81C784 100%);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.battery-indicator::after {
    content: '';
    position: absolute;
    right: -4px;
    top: 8px;
    width: 3px;
    height: 9px;
    background: #fff;
    border-radius: 0 2px 2px 0;
}

.battery-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #FFD700;
}

/* Banner 3: LED Matrix Technology */
#matrix-banner {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 30%, #FFD700 100%);
    color: white;
    overflow: hidden;
}

.matrix-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.light-beams {
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 40%, rgba(255,215,0,0.1) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255,255,255,0.05) 50%, transparent 60%);
    background-size: 80px 80px, 120px 120px;
    animation: lightSweep 4s ease-in-out infinite;
}

@keyframes lightSweep {
    0%, 100% { transform: translateX(-20px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateX(20px) rotate(2deg); opacity: 0.6; }
}

.matrix-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.matrix-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.matrix-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.tech-badge {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    color: #FFD700;
}

.matrix-features {
    display: flex;
    gap: 25px;
    margin: 20px 0;
}

.feature-highlight {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
}

.feature-highlight.active {
    opacity: 1;
    transform: scale(1);
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.feature-highlight:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: #FFD700;
}

.feature-number {
    font-size: 32px;
    font-weight: 700;
    color: #FFD700;
    line-height: 1;
}

.feature-name {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 5px;
    text-align: center;
}

.matrix-description {
    margin-top: 10px;
}

.matrix-description p {
    font-size: 13px;
    opacity: 0.9;
    line-height: 1.4;
}

.matrix-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.headlight-demo {
    position: absolute;
    top: 20px;
    right: 50px;
    width: 120px;
    height: 40px;
    background: linear-gradient(to right, #333, #555);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.led-segments {
    display: flex;
    gap: 3px;
    padding: 5px;
}

.led-segment {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.led-segment.active {
    background: #FFD700;
    box-shadow: 0 0 10px #FFD700;
    animation: ledPulse 2s ease-in-out infinite;
}

@keyframes ledPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.elroq-front {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 280px;
    height: 140px;
}

.front-car {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 8px 25px rgba(255, 215, 0, 0.4));
}

.light-pattern {
    position: absolute;
    top: 50px;
    left: 0;
    width: 200px;
    height: 80px;
    background: linear-gradient(45deg, transparent 0%, rgba(255, 215, 0, 0.1) 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.5s ease;
    transform: rotate(-10deg);
}

.light-pattern.active {
    opacity: 1;
    animation: lightBeam 3s ease-in-out infinite;
}

@keyframes lightBeam {
    0%, 100% { transform: rotate(-10deg) scaleX(1); opacity: 0.3; }
    50% { transform: rotate(-5deg) scaleX(1.2); opacity: 0.7; }
}

/* Banner 4: Simply Clever Features */
#clever-banner {
    background: linear-gradient(135deg, #00A651 0%, #4CAF50 50%, #8BC34A 100%);
    color: white;
    overflow: hidden;
}

.clever-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.2;
}

.clever-pattern {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 70%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px, 70px 70px;
    animation: cleverFloat 12s ease-in-out infinite;
}

@keyframes cleverFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.2; }
    50% { transform: translate(15px, -15px) rotate(180deg); opacity: 0.4; }
}

.clever-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.clever-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.clever-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.clever-badge {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    color: #fff;
}

.clever-solutions {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.solution-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
}

.solution-item.active {
    opacity: 1;
    transform: scale(1);
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.solution-item:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: #FFD700;
}

.solution-icon {
    font-size: 24px;
    margin-bottom: 2px;
}

.solution-name {
    font-size: 11px;
    text-align: center;
    font-weight: 500;
}

.clever-explanation {
    margin-top: 10px;
}

.clever-explanation p {
    font-size: 13px;
    opacity: 0.9;
    line-height: 1.4;
}

.clever-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.elroq-clever {
    position: relative;
    width: 100%;
    height: 100%;
}

.clever-car {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 8px 25px rgba(0, 166, 81, 0.4));
    transition: all 0.5s ease;
}

.solution-highlight {
    position: absolute;
    width: 25px;
    height: 25px;
    background: #FFD700;
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px #FFD700;
    z-index: 3;
}

.solution-highlight.active {
    opacity: 1;
    animation: solutionPulse 2s infinite;
}

@keyframes solutionPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.3); opacity: 0.7; }
}

/* Responsive Design for Elroq Banners */
@media (max-width: 768px) {
    .banner {
        height: 200px;
        flex-direction: column;
    }

    .power-content, .charging-content, .matrix-content, .clever-content {
        padding: 15px 20px;
    }

    .power-visual, .charging-visual, .matrix-visual, .clever-visual {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        width: 100%;
        height: 80px;
        margin-top: 10px;
    }

    .power-stats, .charging-details, .matrix-features, .clever-solutions {
        gap: 10px;
    }

    .stat-box, .detail-item, .feature-highlight, .solution-item {
        padding: 8px 10px;
        font-size: 10px;
    }

    .stat-number, .detail-number, .feature-number {
        font-size: 20px;
    }

    .charge-time {
        font-size: 32px;
    }

    .model-name, .model-text {
        font-size: 20px;
    }

    .power-slogan h2 {
        font-size: 16px;
    }

    .car-image, .charging-car, .front-car, .clever-car {
        height: 60px;
    }
}

/* Banner 5: Premium Configurator */
#configurator-banner {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 30%, #3d3d3d 70%, #1a1a1a 100%);
    color: white;
    overflow: hidden;
    position: relative;
}

.config-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.luxury-pattern {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 30% 30%, rgba(255,215,0,0.1) 2px, transparent 2px),
        radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 1px, transparent 1px);
    background-size: 60px 60px, 40px 40px;
    animation: luxuryFloat 20s ease-in-out infinite;
}

@keyframes luxuryFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); opacity: 0.1; }
    50% { transform: translate(20px, -20px) rotate(180deg); opacity: 0.2; }
}

.config-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.config-header {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-premium {
    height: 40px;
    width: auto;
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(255,215,0,0.5));
}

.config-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.model-premium {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 36px;
    font-weight: 700;
    letter-spacing: 4px;
    color: #FFD700;
    text-shadow: 0 0 20px rgba(255,215,0,0.3);
}

.config-subtitle {
    font-size: 14px;
    font-weight: 300;
    opacity: 0.8;
    color: #ccc;
}

.config-options {
    display: flex;
    gap: 15px;
    margin: 20px 0;
}

.config-category {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.4s ease;
    opacity: 0.6;
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

.config-category.active {
    opacity: 1;
    transform: scale(1);
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.2);
}

.config-category:hover {
    opacity: 0.9;
    transform: scale(1.02);
    border-color: rgba(255, 215, 0, 0.5);
}

.category-icon {
    font-size: 20px;
    margin-bottom: 2px;
}

.category-name {
    font-size: 11px;
    font-weight: 600;
    text-align: center;
}

.category-value {
    font-size: 10px;
    opacity: 0.8;
    text-align: center;
    color: #FFD700;
}

.config-price {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
}

.price-label {
    font-size: 12px;
    opacity: 0.7;
}

.price-amount {
    font-size: 28px;
    font-weight: 700;
    color: #FFD700;
    text-shadow: 0 0 15px rgba(255,215,0,0.3);
}

.config-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 400px;
    height: 200px;
}

.car-360 {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.config-car {
    width: 350px;
    height: 180px;
    object-fit: contain;
    filter: drop-shadow(0 15px 40px rgba(0, 0, 0, 0.6)) brightness(1.1);
    transition: all 0.6s ease;
    animation: carRotate 8s ease-in-out infinite;
}

@keyframes carRotate {
    0%, 100% { transform: rotateY(0deg) scale(1); }
    25% { transform: rotateY(5deg) scale(1.02); }
    50% { transform: rotateY(0deg) scale(1.05); }
    75% { transform: rotateY(-5deg) scale(1.02); }
}

.rotation-indicator {
    position: absolute;
    bottom: 10px;
    right: 20px;
    font-size: 12px;
    font-weight: 600;
    color: #FFD700;
    opacity: 0.7;
    animation: rotateText 2s ease-in-out infinite;
}

@keyframes rotateText {
    0%, 100% { opacity: 0.7; transform: rotate(0deg); }
    50% { opacity: 1; transform: rotate(180deg); }
}

.config-highlights {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Banner 6: Sustainability */
#sustainability-banner {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 30%, #81C784 70%, #A5D6A7 100%);
    color: white;
    overflow: hidden;
}

.eco-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.2;
}

.nature-elements {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 40%, rgba(255,255,255,0.1) 3px, transparent 3px),
        radial-gradient(circle at 80% 60%, rgba(255,255,255,0.08) 2px, transparent 2px);
    background-size: 80px 80px, 120px 120px;
    animation: natureFlow 15s ease-in-out infinite;
}

@keyframes natureFlow {
    0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.2; }
    50% { transform: translate(15px, -10px) scale(1.1); opacity: 0.3; }
}

.eco-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.eco-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-eco {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(76,175,80,0.5));
}

.eco-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.model-eco {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 30px;
    font-weight: 700;
    letter-spacing: 3px;
    color: #A5D6A7;
}

.eco-badge {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    color: #fff;
}

.eco-metrics {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.metric-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.05);
}

.metric-card.active {
    opacity: 1;
    transform: scale(1);
    border-color: #A5D6A7;
    background: rgba(165, 214, 167, 0.1);
    box-shadow: 0 0 20px rgba(165, 214, 167, 0.3);
}

.metric-card:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: #A5D6A7;
}

.metric-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #A5D6A7;
    line-height: 1;
}

.metric-label {
    font-size: 11px;
    opacity: 0.9;
    margin-top: 3px;
    text-align: center;
}

.eco-message p {
    font-size: 13px;
    opacity: 0.9;
    line-height: 1.4;
}

.model-tab:hover {
    opacity: 1;
    border-color: #00A651;
}

.model-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.model-name {
    font-size: 24px;
    font-weight: 600;
    color: #00A651;
}

.model-features {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.feature-badge {
    background: rgba(0, 166, 81, 0.2);
    color: #00A651;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid rgba(0, 166, 81, 0.3);
}

.model-price {
    font-size: 14px;
    opacity: 0.8;
    font-weight: 500;
}

.showcase-car {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.car-image {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transition: all 0.5s ease;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.car-reflection {
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to bottom, rgba(255,255,255,0.1), transparent);
    transform: scaleY(-1);
    opacity: 0.3;
}

/* Banner 3: Sustainability */
#sustainability-banner {
    background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%);
    color: white;
}

.sustainability-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.eco-particles {
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 60% 70%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 60px 60px, 80px 80px, 100px 100px;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-20px); }
}

.sustainability-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
}

.eco-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.skoda-eco-logo {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 2px;
}

.eco-title {
    font-size: 20px;
    font-weight: 300;
    opacity: 0.9;
}

.eco-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #FFD700;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 2px;
}

.eco-message p {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 500;
}

.sustainability-visual {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    width: 300px;
    height: 160px;
}

.electric-car {
    width: 200px;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 100"><path d="M30 70 Q40 50 60 55 L140 55 Q160 50 170 70 L190 70 Q195 75 190 85 L175 90 Q165 95 155 90 L65 90 Q55 95 45 90 L30 85 Q25 75 30 70 Z" fill="rgba(255,255,255,0.8)"/><circle cx="60" cy="85" r="12" fill="rgba(255,255,255,0.9)"/><circle cx="140" cy="85" r="12" fill="rgba(255,255,255,0.9)"/><path d="M80 65 L85 55 L90 65 L95 55 L100 65" stroke="%23FFD700" stroke-width="2" fill="none"/></svg>') no-repeat center;
    background-size: contain;
    position: relative;
}

.charging-animation {
    position: absolute;
    top: 20px;
    left: 80px;
    width: 40px;
    height: 20px;
    background: #FFD700;
    border-radius: 10px;
    opacity: 0;
    animation: charging 3s ease-in-out infinite;
}

@keyframes charging {
    0%, 100% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

.eco-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.leaf {
    position: absolute;
    font-size: 24px;
    animation: leafFloat 4s ease-in-out infinite;
}

.leaf-1 {
    top: 20px;
    right: 50px;
    animation-delay: 0s;
}

.leaf-2 {
    top: 60px;
    right: 20px;
    animation-delay: 1s;
}

.leaf-3 {
    top: 100px;
    right: 80px;
    animation-delay: 2s;
}

@keyframes leafFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-10px) rotate(10deg); opacity: 1; }
}

/* Banner 4: Czech Heritage */
#heritage-banner {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #CD853F 100%);
    color: white;
}

.heritage-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.2;
}

.czech-pattern {
    width: 100%;
    height: 100%;
    background-image: 
        repeating-linear-gradient(45deg, rgba(255,255,255,0.1) 0px, rgba(255,255,255,0.1) 2px, transparent 2px, transparent 10px),
        repeating-linear-gradient(-45deg, rgba(255,255,255,0.1) 0px, rgba(255,255,255,0.1) 2px, transparent 2px, transparent 10px);
}

.heritage-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
}

.heritage-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.heritage-year {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 36px;
    font-weight: 700;
    color: #FFD700;
}

.heritage-title {
    font-size: 22px;
    font-weight: 300;
}

.heritage-timeline {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.timeline-point {
    padding: 8px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 600;
    opacity: 0.7;
}

.timeline-point.active {
    background: #FFD700;
    border-color: #FFD700;
    color: #8B4513;
    opacity: 1;
    transform: scale(1.1);
}

.timeline-point:hover {
    opacity: 1;
    border-color: #FFD700;
}

.heritage-info {
    max-width: 400px;
}

.heritage-text {
    font-size: 14px;
    line-height: 1.5;
    opacity: 0.9;
}

.heritage-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 160px;
}

.vintage-car, .modern-car {
    position: absolute;
    width: 120px;
    height: 60px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transition: all 0.5s ease;
}

.vintage-car {
    top: 20px;
    left: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 60"><path d="M20 45 Q25 35 35 38 L85 38 Q95 35 100 45 L110 45 Q115 48 110 52 L100 55 Q95 58 90 55 L40 55 Q35 58 30 55 L20 52 Q15 48 20 45 Z" fill="rgba(255,255,255,0.6)"/><circle cx="40" cy="50" r="8" fill="rgba(255,255,255,0.7)"/><circle cx="80" cy="50" r="8" fill="rgba(255,255,255,0.7)"/></svg>') no-repeat center;
    opacity: 0.8;
}

.modern-car {
    bottom: 20px;
    right: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 60"><path d="M15 40 Q20 25 40 30 L80 30 Q100 25 105 40 L115 40 Q118 43 115 48 L105 52 Q100 55 95 52 L35 52 Q30 55 25 52 L15 48 Q12 43 15 40 Z" fill="rgba(255,255,255,0.9)"/><circle cx="35" cy="47" r="10" fill="rgba(255,255,255,1)"/><circle cx="85" cy="47" r="10" fill="rgba(255,255,255,1)"/><path d="M50 35 L55 25 L60 35 L65 25 L70 35" stroke="%2300A651" stroke-width="2" fill="none"/></svg>') no-repeat center;
}

.heritage-bridge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, rgba(255,255,255,0.3), #FFD700, rgba(255,255,255,0.3));
    animation: bridgeGlow 3s ease-in-out infinite;
}

@keyframes bridgeGlow {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scaleX(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scaleX(1.2); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .banner {
        width: 100%;
        max-width: 970px;
    }
}

.eco-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.nature-scene {
    position: relative;
    width: 100%;
    height: 100%;
}

.eco-car {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sustainable-car {
    width: 300px;
    height: 150px;
    object-fit: contain;
    filter: drop-shadow(0 8px 25px rgba(76, 175, 80, 0.6)) brightness(1.1);
    transition: all 0.5s ease;
}

.eco-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.leaf-particle {
    position: absolute;
    font-size: 20px;
    opacity: 0.7;
    animation: ecoFloat 4s ease-in-out infinite;
}

.leaf-particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.leaf-particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1.5s;
}

.leaf-particle:nth-child(3) {
    top: 80%;
    left: 30%;
    animation-delay: 3s;
}

@keyframes ecoFloat {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-15px) rotate(180deg); opacity: 1; }
}

@keyframes energyPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
}

@keyframes recycleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Banner 7: Technology Showcase */
#technology-banner {
    background: linear-gradient(135deg, #0D47A1 0%, #1976D2 30%, #42A5F5 70%, #90CAF9 100%);
    color: white;
    overflow: hidden;
}

.tech-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.2;
}

.digital-grid {
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: digitalFlow 10s linear infinite;
}

@keyframes digitalFlow {
    0% { transform: translate(0, 0); opacity: 0.2; }
    100% { transform: translate(30px, 30px); opacity: 0.4; }
}

.tech-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.tech-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-tech {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(33,150,243,0.5));
}

.tech-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.model-tech {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 30px;
    font-weight: 700;
    letter-spacing: 3px;
    color: #90CAF9;
}

.tech-badge {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    color: #fff;
}

.tech-features {
    display: flex;
    gap: 18px;
    margin: 20px 0;
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.05);
}

.tech-item.active {
    opacity: 1;
    transform: scale(1);
    border-color: #90CAF9;
    background: rgba(144, 202, 249, 0.1);
    box-shadow: 0 0 20px rgba(144, 202, 249, 0.3);
}

.tech-item:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: #90CAF9;
}

.tech-icon {
    font-size: 22px;
    margin-bottom: 2px;
}

.tech-name {
    font-size: 11px;
    font-weight: 600;
    text-align: center;
}

.tech-detail {
    font-size: 9px;
    opacity: 0.8;
    text-align: center;
    color: #90CAF9;
}

.tech-description p {
    font-size: 13px;
    opacity: 0.9;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .banner {
        height: 200px;
    }

    .clever-content, .showcase-content, .sustainability-content, .heritage-content {
        padding: 15px 20px;
    }

    .clever-features, .model-selector, .eco-stats, .heritage-timeline {
        gap: 10px;
    }

    .clever-car, .showcase-car, .sustainability-visual, .heritage-visual {
        width: 200px;
        right: 20px;
    }
}

.tech-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.cockpit-view {
    position: relative;
    width: 100%;
    height: 100%;
}

.dashboard-screen {
    position: absolute;
    top: 20px;
    left: 50px;
    width: 80px;
    height: 40px;
    background: linear-gradient(135deg, #1976D2, #42A5F5);
    border-radius: 8px;
    border: 2px solid #90CAF9;
    animation: screenGlow 2s ease-in-out infinite;
}

@keyframes screenGlow {
    0%, 100% { box-shadow: 0 0 10px rgba(144, 202, 249, 0.5); }
    50% { box-shadow: 0 0 20px rgba(144, 202, 249, 0.8); }
}

.tech-car {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.technology-car {
    width: 300px;
    height: 150px;
    object-fit: contain;
    filter: drop-shadow(0 8px 25px rgba(33, 150, 243, 0.6)) brightness(1.2);
    transition: all 0.5s ease;
}

.tech-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

/* Banner 8: Performance Dynamics */
#performance-banner {
    background: linear-gradient(135deg, #B71C1C 0%, #D32F2F 30%, #F44336 70%, #FF5722 100%);
    color: white;
    overflow: hidden;
}

.performance-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.3;
}

.speed-lines {
    width: 100%;
    height: 100%;
    background-image:
        repeating-linear-gradient(90deg, transparent 0px, transparent 8px, rgba(255,255,255,0.1) 8px, rgba(255,255,255,0.1) 10px),
        repeating-linear-gradient(45deg, transparent 0px, transparent 15px, rgba(255,255,255,0.05) 15px, rgba(255,255,255,0.05) 17px);
    animation: speedMotion 3s linear infinite;
}

@keyframes speedMotion {
    0% { transform: translateX(0); }
    100% { transform: translateX(20px); }
}

.performance-content {
    position: relative;
    z-index: 2;
    padding: 25px 35px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.performance-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-performance {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1) drop-shadow(0 0 10px rgba(255,87,34,0.5));
}

.performance-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.model-performance {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 30px;
    font-weight: 700;
    letter-spacing: 3px;
    color: #FFAB91;
}

.performance-badge {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    color: #fff;
}

.performance-stats {
    display: flex;
    gap: 20px;
    margin: 15px 0;
}

.perf-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.05);
}

.perf-stat.active {
    opacity: 1;
    transform: scale(1);
    border-color: #FFAB91;
    background: rgba(255, 171, 145, 0.1);
    box-shadow: 0 0 20px rgba(255, 171, 145, 0.3);
}

.perf-stat:hover {
    opacity: 1;
    transform: scale(1.05);
    border-color: #FFAB91;
}

.stat-icon {
    font-size: 20px;
    margin-bottom: 3px;
}

.stat-big {
    font-size: 22px;
    font-weight: 700;
    color: #FFAB91;
    line-height: 1;
}

.stat-small {
    font-size: 9px;
    opacity: 0.9;
    margin-top: 2px;
    text-align: center;
}

.performance-modes {
    display: flex;
    gap: 12px;
    margin-top: 10px;
}

.drive-mode {
    padding: 8px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 11px;
    font-weight: 600;
    opacity: 0.7;
    background: rgba(255, 255, 255, 0.05);
}

.drive-mode.active {
    opacity: 1;
    transform: scale(1.05);
    color: white;
    border-color: transparent;
}

.drive-mode:hover {
    opacity: 1;
    border-color: rgba(255, 255, 255, 0.5);
}

.performance-visual {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    height: 180px;
}

.dynamic-scene {
    position: relative;
    width: 100%;
    height: 100%;
}

.performance-car {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dynamic-car {
    width: 300px;
    height: 150px;
    object-fit: contain;
    filter: drop-shadow(0 8px 25px rgba(255, 87, 34, 0.8)) brightness(1.2);
    transition: all 0.5s ease;
}

.motion-blur {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 60px;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.5s ease;
}

.speedometer {
    position: absolute;
    bottom: 10px;
    right: 20px;
    width: 60px;
    height: 60px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.speed-needle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 20px;
    background: #FFAB91;
    transform-origin: bottom center;
    transform: translate(-50%, -100%) rotate(0deg);
    transition: transform 0.8s ease;
}

.speed-value {
    font-size: 12px;
    font-weight: 700;
    color: #FFAB91;
    margin-top: 15px;
}

@keyframes speedBlur {
    0%, 100% { opacity: 0; transform: translateX(0); }
    50% { opacity: 1; transform: translateX(50px); }
}

@keyframes powerPulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

@keyframes torqueWave {
    0%, 100% { opacity: 0.3; transform: translateY(0); }
    50% { opacity: 0.8; transform: translateY(-10px); }
}

/* Additional Professional Animations */
@keyframes luxuryShimmer {
    0%, 100% { background-position: -200% 0; }
    50% { background-position: 200% 0; }
}

@keyframes premiumGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
}

@keyframes professionalFloat {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-5px) scale(1.02); }
}

/* Enhanced Responsive Design for New Banners */
@media (max-width: 768px) {
    .config-visual, .eco-visual, .tech-visual, .performance-visual {
        width: 250px;
        height: 120px;
        right: 20px;
    }

    .config-car, .sustainable-car, .technology-car, .dynamic-car {
        width: 200px;
        height: 100px;
    }

    .config-options, .eco-metrics, .tech-features, .performance-stats {
        gap: 8px;
    }

    .config-category, .metric-card, .tech-item, .perf-stat {
        padding: 8px;
        font-size: 9px;
    }

    .model-premium, .model-eco, .model-tech, .model-performance {
        font-size: 24px;
    }

    .price-amount {
        font-size: 20px;
    }

    .performance-modes {
        gap: 8px;
    }

    .drive-mode {
        padding: 6px 12px;
        font-size: 10px;
    }
}

// Skoda Interactive Banners JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initElroqHeroBanner();
    initPowerBalanceBanner();
    initChargingBanner();
    initMatrixBanner();
    initCleverBanner();
    initConfiguratorBanner();
    initSustainabilityBanner();
    initTechnologyBanner();
    initPerformanceBanner();
});

// NEW BANNER: Elroq Hero Launch
function initElroqHeroBanner() {
    const featurePills = document.querySelectorAll('.feature-pill');
    const heroCar = document.getElementById('hero-car');
    const carGlow = document.getElementById('car-glow');
    const featureHighlight = document.getElementById('feature-highlight');
    const highlightText = document.getElementById('highlight-text');
    const ctaButton = document.getElementById('hero-cta');

    const features = {
        range: {
            text: 'Kombine 422 km • Şehir içi 543 km menzil',
            glow: 'rgba(76,175,80,0.6)',
            carFilter: 'drop-shadow(0 15px 40px rgba(76,175,80,0.6)) brightness(1.1)',
            glowColor: 'radial-gradient(ellipse, rgba(76,175,80,0.4) 0%, transparent 70%)'
        },
        charging: {
            text: 'DC Hızlı Şarj: %10\'dan %80\'e 24 dakika',
            glow: 'rgba(255,215,0,0.6)',
            carFilter: 'drop-shadow(0 15px 40px rgba(255,215,0,0.6)) brightness(1.1)',
            glowColor: 'radial-gradient(ellipse, rgba(255,215,0,0.4) 0%, transparent 70%)'
        },
        power: {
            text: '204 PS güç • 310 Nm tork • 8.0s 0-100',
            glow: 'rgba(33,150,243,0.6)',
            carFilter: 'drop-shadow(0 15px 40px rgba(33,150,243,0.6)) brightness(1.1)',
            glowColor: 'radial-gradient(ellipse, rgba(33,150,243,0.4) 0%, transparent 70%)'
        },
        tech: {
            text: 'Full LED Matrix • 36 segment • Adaptif ışık',
            glow: 'rgba(156,39,176,0.6)',
            carFilter: 'drop-shadow(0 15px 40px rgba(156,39,176,0.6)) brightness(1.1)',
            glowColor: 'radial-gradient(ellipse, rgba(156,39,176,0.4) 0%, transparent 70%)'
        }
    };

    // Auto-cycle through features
    let currentFeatureIndex = 0;
    const featureKeys = Object.keys(features);

    function cycleFeatures() {
        const currentFeature = featureKeys[currentFeatureIndex];
        const feature = features[currentFeature];

        // Update active pill
        featurePills.forEach(pill => pill.classList.remove('active'));
        document.querySelector(`[data-feature="${currentFeature}"]`).classList.add('active');

        // Update car appearance
        heroCar.style.filter = feature.carFilter;
        carGlow.style.background = feature.glowColor;

        // Update highlight text
        highlightText.textContent = feature.text;
        featureHighlight.classList.add('show');

        currentFeatureIndex = (currentFeatureIndex + 1) % featureKeys.length;
    }

    // Start auto-cycle
    cycleFeatures();
    const autoInterval = setInterval(cycleFeatures, 3000);

    // Manual feature selection
    featurePills.forEach(pill => {
        pill.addEventListener('click', function() {
            clearInterval(autoInterval);
            const featureType = this.dataset.feature;
            const feature = features[featureType];

            if (feature) {
                featurePills.forEach(p => p.classList.remove('active'));
                this.classList.add('active');

                heroCar.style.filter = feature.carFilter;
                carGlow.style.background = feature.glowColor;
                highlightText.textContent = feature.text;
                featureHighlight.classList.add('show');
            }
        });
    });

    // CTA button interaction
    ctaButton.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);

        // Simulate navigation (you can replace with actual URL)
        console.log('Navigating to Elroq details page...');
        // window.open('https://www.skoda.com.tr/modeller/elroq', '_blank');
    });

    // Banner hover effects
    const banner = document.getElementById('elroq-hero-banner');
    banner.addEventListener('mouseenter', function() {
        heroCar.style.transform = 'scale(1.05) translateX(10px)';
        carGlow.style.transform = 'translate(-50%, -50%) scale(1.2)';
    });

    banner.addEventListener('mouseleave', function() {
        heroCar.style.transform = '';
        carGlow.style.transform = 'translate(-50%, -50%) scale(1)';
    });
}

// Banner 1: Power & Balance
function initPowerBalanceBanner() {
    const statBoxes = document.querySelectorAll('.stat-box');
    const powerIndicator = document.getElementById('power-indicator');
    const carImage = document.querySelector('.car-image');

    const stats = {
        power: {
            highlight: () => {
                carImage.style.filter = 'drop-shadow(0 10px 30px rgba(255, 215, 0, 0.8)) brightness(1.1)';
                powerIndicator.style.background = 'linear-gradient(to right, #FFD700, #FFA000)';
            }
        },
        torque: {
            highlight: () => {
                carImage.style.filter = 'drop-shadow(0 10px 30px rgba(0, 166, 81, 0.8)) brightness(1.1)';
                powerIndicator.style.background = 'linear-gradient(to right, #00A651, #4CAF50)';
            }
        },
        acceleration: {
            highlight: () => {
                carImage.style.filter = 'drop-shadow(0 10px 30px rgba(33, 150, 243, 0.8)) brightness(1.1)';
                powerIndicator.style.background = 'linear-gradient(to right, #2196F3, #03A9F4)';
            }
        },
        range: {
            highlight: () => {
                carImage.style.filter = 'drop-shadow(0 10px 30px rgba(76, 175, 80, 0.8)) brightness(1.1)';
                powerIndicator.style.background = 'linear-gradient(to right, #4CAF50, #8BC34A)';
            }
        }
    };

    statBoxes.forEach(box => {
        box.addEventListener('click', function() {
            const stat = this.dataset.stat;

            // Remove active class from all boxes
            statBoxes.forEach(b => b.classList.remove('active'));

            // Add active class to clicked box
            this.classList.add('active');

            // Apply stat-specific highlighting
            if (stats[stat]) {
                stats[stat].highlight();
                powerIndicator.classList.add('active');
            }
        });

        box.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#FFD700';
            }
        });

        box.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }
        });
    });

    // Auto-cycle through stats
    let currentStatIndex = 0;
    setInterval(() => {
        currentStatIndex = (currentStatIndex + 1) % statBoxes.length;
        statBoxes[currentStatIndex].click();
    }, 3000);
}

// Banner 2: Electric Charging
function initChargingBanner() {
    const batteryLevel = document.getElementById('battery-level');
    const batteryPercentage = document.getElementById('battery-percentage');
    const chargingCar = document.querySelector('.charging-car');

    let currentCharge = 10;
    let isCharging = true;

    function updateBattery() {
        if (isCharging && currentCharge < 80) {
            currentCharge += 1;
            batteryPercentage.textContent = currentCharge + '%';

            // Update battery visual - fixed calculation
            const batteryFillPercentage = ((currentCharge - 10) / (80 - 10)) * 100;
            batteryLevel.style.width = batteryFillPercentage + '%';
            batteryLevel.style.background = `linear-gradient(to right, #4CAF50 0%, #66BB6A 50%, #81C784 100%)`;

            // Car glow effect based on charging
            const glowIntensity = (currentCharge - 10) / 70;
            chargingCar.style.filter = `drop-shadow(0 8px 25px rgba(66, 165, 245, ${0.4 + glowIntensity * 0.6})) brightness(${1 + glowIntensity * 0.2})`;

        } else if (currentCharge >= 80) {
            // Reset charging cycle
            isCharging = false;
            setTimeout(() => {
                currentCharge = 10;
                batteryPercentage.textContent = '10%';
                batteryLevel.style.width = '0%';
                isCharging = true;
            }, 3000);
        }
    }

    // Update battery every 200ms for smoother animation
    setInterval(updateBattery, 200);

    // Add hover effects to detail items
    const detailItems = document.querySelectorAll('.detail-item');
    detailItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.borderColor = '#FFD700';
            this.style.background = 'rgba(255, 215, 0, 0.1)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            this.style.background = 'rgba(255, 255, 255, 0.05)';
        });
    });
}

// Banner 3: LED Matrix Technology
function initMatrixBanner() {
    const featureHighlights = document.querySelectorAll('.feature-highlight');
    const matrixDesc = document.getElementById('matrix-desc');
    const ledSegments = document.querySelectorAll('.led-segment');
    const lightPattern = document.getElementById('light-pattern');

    const features = {
        segments: {
            description: "36 ayrı LED segmenti ile yalnızca ihtiyaç duyulan bölgelere ışık yönlendirir",
            ledPattern: [true, true, true, false, false, true]
        },
        modes: {
            description: "Şehir, otoyol, kırsal alan ve kötü hava koşulları için 4 farklı aydınlatma modu",
            ledPattern: [true, false, true, false, true, false]
        },
        adaptive: {
            description: "Karşıdan gelen araçları algılayarak otomatik olarak ışık dağılımını ayarlar",
            ledPattern: [false, true, true, true, true, false]
        }
    };

    featureHighlights.forEach(highlight => {
        highlight.addEventListener('click', function() {
            const feature = this.dataset.feature;

            // Remove active class from all highlights
            featureHighlights.forEach(h => h.classList.remove('active'));

            // Add active class to clicked highlight
            this.classList.add('active');

            // Update description
            if (features[feature]) {
                matrixDesc.textContent = features[feature].description;

                // Update LED pattern
                const pattern = features[feature].ledPattern;
                ledSegments.forEach((segment, index) => {
                    segment.classList.remove('active');
                    if (pattern[index]) {
                        setTimeout(() => {
                            segment.classList.add('active');
                        }, index * 100);
                    }
                });

                // Activate light pattern
                lightPattern.classList.add('active');
                setTimeout(() => {
                    lightPattern.classList.remove('active');
                }, 3000);
            }
        });

        highlight.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#FFD700';
            }
        });

        highlight.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 215, 0, 0.3)';
            }
        });
    });

    // Auto-cycle through features
    let currentFeatureIndex = 0;
    setInterval(() => {
        currentFeatureIndex = (currentFeatureIndex + 1) % featureHighlights.length;
        featureHighlights[currentFeatureIndex].click();
    }, 5000);
}

// Banner 4: Simply Clever Features
function initCleverBanner() {
    const solutionItems = document.querySelectorAll('.solution-item');
    const cleverDesc = document.getElementById('clever-desc');
    const solutionHighlight = document.getElementById('solution-highlight');
    const cleverCar = document.querySelector('.clever-car');

    const solutions = {
        umbrella: {
            description: "Bagaj kapağında saklama bölmesi - pratik çözümler her detayda",
            position: { top: '30%', left: '20%' },
            carEffect: () => {
                cleverCar.style.filter = 'drop-shadow(0 8px 25px rgba(255, 215, 0, 0.6)) brightness(1.1)';
            }
        },
        wireless: {
            description: "Kablosuz şarj teknolojisi - telefonunuz her zaman şarjda",
            position: { top: '50%', left: '40%' },
            carEffect: () => {
                cleverCar.style.filter = 'drop-shadow(0 8px 25px rgba(33, 150, 243, 0.6)) brightness(1.1)';
            }
        },
        storage: {
            description: "Çift kademeli bagaj sistemi - daha fazla alan, daha iyi organizasyon",
            position: { top: '70%', left: '80%' },
            carEffect: () => {
                cleverCar.style.filter = 'drop-shadow(0 8px 25px rgba(76, 175, 80, 0.6)) brightness(1.1)';
            }
        },
        pedal: {
            description: "Sanal pedal teknolojisi - ayağınızı hareket ettirerek bagajı açın",
            position: { top: '80%', left: '50%' },
            carEffect: () => {
                cleverCar.style.filter = 'drop-shadow(0 8px 25px rgba(156, 39, 176, 0.6)) brightness(1.1)';
            }
        }
    };

    solutionItems.forEach(item => {
        item.addEventListener('click', function() {
            const solution = this.dataset.solution;

            // Remove active class from all items
            solutionItems.forEach(s => s.classList.remove('active'));

            // Add active class to clicked item
            this.classList.add('active');

            // Update description and effects
            if (solutions[solution]) {
                cleverDesc.textContent = solutions[solution].description;

                // Apply car effect
                solutions[solution].carEffect();

                // Update highlight position
                const pos = solutions[solution].position;
                solutionHighlight.style.top = pos.top;
                solutionHighlight.style.left = pos.left;
                solutionHighlight.classList.add('active');

                // Reset and re-add active class for animation
                setTimeout(() => {
                    solutionHighlight.classList.remove('active');
                    setTimeout(() => {
                        solutionHighlight.classList.add('active');
                    }, 50);
                }, 100);
            }
        });

        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#FFD700';
            }
        });

        item.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            }
        });
    });

    // Auto-cycle through solutions
    let currentSolutionIndex = 0;
    setInterval(() => {
        currentSolutionIndex = (currentSolutionIndex + 1) % solutionItems.length;
        solutionItems[currentSolutionIndex].click();
    }, 4500);
}

// Banner 5: Premium Configurator
function initConfiguratorBanner() {
    const configCategories = document.querySelectorAll('.config-category');
    const configCar = document.getElementById('config-car');
    const configPrice = document.getElementById('config-price');

    const configurations = {
        exterior: {
            options: ['Kristal Siyah', 'Arktik Beyaz', 'Gümüş Metalik', 'Mavi Metalik'],
            prices: [1299000, 1299000, 1315000, 1325000],
            carEffect: () => {
                configCar.style.filter = 'drop-shadow(0 15px 40px rgba(0, 0, 0, 0.8)) brightness(1.2) contrast(1.1)';
            }
        },
        interior: {
            options: ['Siyah Deri', 'Bej Deri', 'Spor Kumaş', 'Premium Alcantara'],
            prices: [1299000, 1340000, 1320000, 1380000],
            carEffect: () => {
                configCar.style.filter = 'drop-shadow(0 15px 40px rgba(139, 69, 19, 0.6)) brightness(1.1) sepia(0.2)';
            }
        },
        wheels: {
            options: ['19" Aero', '20" Sport', '21" Performance', '19" Kış'],
            prices: [1299000, 1350000, 1420000, 1315000],
            carEffect: () => {
                configCar.style.filter = 'drop-shadow(0 15px 40px rgba(192, 192, 192, 0.8)) brightness(1.15)';
            }
        },
        tech: {
            options: ['Matrix LED', 'Premium Ses', 'Panoramik Cam', 'Tam Donanım'],
            prices: [1299000, 1335000, 1365000, 1450000],
            carEffect: () => {
                configCar.style.filter = 'drop-shadow(0 15px 40px rgba(0, 150, 255, 0.6)) brightness(1.2) hue-rotate(10deg)';
            }
        }
    };

    configCategories.forEach(category => {
        category.addEventListener('click', function() {
            const categoryType = this.dataset.category;

            // Remove active class from all categories
            configCategories.forEach(c => c.classList.remove('active'));

            // Add active class to clicked category
            this.classList.add('active');

            // Update configuration
            if (configurations[categoryType]) {
                const config = configurations[categoryType];
                const randomIndex = Math.floor(Math.random() * config.options.length);

                // Update category value
                const valueElement = this.querySelector('.category-value');
                valueElement.textContent = config.options[randomIndex];

                // Update price
                configPrice.textContent = '₺' + config.prices[randomIndex].toLocaleString('tr-TR');

                // Apply car effect
                config.carEffect();

                // Add rotation effect
                configCar.style.transform = 'rotateY(360deg) scale(1.05)';
                setTimeout(() => {
                    configCar.style.transform = 'rotateY(0deg) scale(1)';
                }, 600);
            }
        });

        category.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.02)';
                this.style.borderColor = 'rgba(255, 215, 0, 0.5)';
            }
        });

        category.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            }
        });
    });

    // Auto-cycle through categories
    let currentConfigIndex = 0;
    setInterval(() => {
        currentConfigIndex = (currentConfigIndex + 1) % configCategories.length;
        configCategories[currentConfigIndex].click();
    }, 6000);
}

// Banner 6: Sustainability
function initSustainabilityBanner() {
    const metricCards = document.querySelectorAll('.metric-card');
    const ecoDesc = document.getElementById('eco-desc');
    const ecoEffects = document.getElementById('eco-effects');
    const sustainableCar = document.querySelector('.sustainable-car');

    const metrics = {
        emission: {
            description: "Tamamen elektrikli Elroq ile çevreye saygılı sürüş deneyimi",
            effect: () => {
                sustainableCar.style.filter = 'drop-shadow(0 8px 25px rgba(76, 175, 80, 0.6)) brightness(1.1)';
                ecoEffects.style.animation = 'ecoFloat 3s ease-in-out infinite';
            }
        },
        energy: {
            description: "Düşük enerji tüketimi ile uzun menzil ve ekonomik sürüş",
            effect: () => {
                sustainableCar.style.filter = 'drop-shadow(0 8px 25px rgba(255, 193, 7, 0.6)) brightness(1.15)';
                ecoEffects.style.animation = 'energyPulse 2s ease-in-out infinite';
            }
        },
        recycling: {
            description: "Geri dönüştürülebilir malzemeler ile sürdürülebilir üretim",
            effect: () => {
                sustainableCar.style.filter = 'drop-shadow(0 8px 25px rgba(0, 150, 136, 0.6)) brightness(1.1)';
                ecoEffects.style.animation = 'recycleRotate 4s linear infinite';
            }
        }
    };

    metricCards.forEach(card => {
        card.addEventListener('click', function() {
            const metric = this.dataset.metric;

            // Remove active class from all cards
            metricCards.forEach(c => c.classList.remove('active'));

            // Add active class to clicked card
            this.classList.add('active');

            // Update description and effects
            if (metrics[metric]) {
                ecoDesc.textContent = metrics[metric].description;
                metrics[metric].effect();
            }
        });

        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#A5D6A7';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }
        });
    });

    // Auto-cycle through metrics
    let currentMetricIndex = 0;
    setInterval(() => {
        currentMetricIndex = (currentMetricIndex + 1) % metricCards.length;
        metricCards[currentMetricIndex].click();
    }, 5000);
}

// Banner 7: Technology Showcase
function initTechnologyBanner() {
    const techItems = document.querySelectorAll('.tech-item');
    const techDesc = document.getElementById('tech-desc');
    const techOverlay = document.getElementById('tech-overlay');
    const technologyCar = document.querySelector('.technology-car');

    const technologies = {
        infotainment: {
            description: "13\" dokunmatik ekran ile tüm araç fonksiyonlarını kontrol edin",
            effect: () => {
                technologyCar.style.filter = 'drop-shadow(0 8px 25px rgba(33, 150, 243, 0.6)) brightness(1.2)';
                techOverlay.style.background = 'radial-gradient(circle, rgba(33,150,243,0.3) 0%, transparent 70%)';
            }
        },
        assistant: {
            description: "Laura sesli asistan ile araçla doğal dilde konuşun",
            effect: () => {
                technologyCar.style.filter = 'drop-shadow(0 8px 25px rgba(156, 39, 176, 0.6)) brightness(1.15)';
                techOverlay.style.background = 'radial-gradient(circle, rgba(156,39,176,0.3) 0%, transparent 70%)';
            }
        },
        connectivity: {
            description: "5G bağlantı ile her zaman online kalın ve güncellemeleri alın",
            effect: () => {
                technologyCar.style.filter = 'drop-shadow(0 8px 25px rgba(0, 150, 136, 0.6)) brightness(1.1)';
                techOverlay.style.background = 'radial-gradient(circle, rgba(0,150,136,0.3) 0%, transparent 70%)';
            }
        },
        safety: {
            description: "9 hava yastığı ve gelişmiş güvenlik sistemleri ile maksimum koruma",
            effect: () => {
                technologyCar.style.filter = 'drop-shadow(0 8px 25px rgba(244, 67, 54, 0.6)) brightness(1.1)';
                techOverlay.style.background = 'radial-gradient(circle, rgba(244,67,54,0.3) 0%, transparent 70%)';
            }
        }
    };

    techItems.forEach(item => {
        item.addEventListener('click', function() {
            const tech = this.dataset.tech;

            // Remove active class from all items
            techItems.forEach(t => t.classList.remove('active'));

            // Add active class to clicked item
            this.classList.add('active');

            // Update description and effects
            if (technologies[tech]) {
                techDesc.textContent = technologies[tech].description;
                technologies[tech].effect();

                // Activate overlay
                techOverlay.style.opacity = '1';
                setTimeout(() => {
                    techOverlay.style.opacity = '0';
                }, 2000);
            }
        });

        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#2196F3';
            }
        });

        item.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }
        });
    });

    // Auto-cycle through technologies
    let currentTechIndex = 0;
    setInterval(() => {
        currentTechIndex = (currentTechIndex + 1) % techItems.length;
        techItems[currentTechIndex].click();
    }, 4000);
}

// Banner 8: Performance Dynamics
function initPerformanceBanner() {
    const perfStats = document.querySelectorAll('.perf-stat');
    const driveModes = document.querySelectorAll('.drive-mode');
    const motionBlur = document.getElementById('motion-blur');
    const speedometer = document.getElementById('speedometer');
    const dynamicCar = document.querySelector('.dynamic-car');

    const performanceData = {
        acceleration: {
            effect: () => {
                dynamicCar.style.filter = 'drop-shadow(0 8px 25px rgba(255, 87, 34, 0.8)) brightness(1.2)';
                motionBlur.style.background = 'linear-gradient(90deg, transparent 0%, rgba(255,87,34,0.3) 50%, transparent 100%)';
                motionBlur.style.animation = 'speedBlur 2s ease-out infinite';
                updateSpeedometer(100);
            }
        },
        power: {
            effect: () => {
                dynamicCar.style.filter = 'drop-shadow(0 8px 25px rgba(255, 215, 0, 0.8)) brightness(1.3)';
                motionBlur.style.background = 'linear-gradient(90deg, transparent 0%, rgba(255,215,0,0.4) 50%, transparent 100%)';
                motionBlur.style.animation = 'powerPulse 1.5s ease-in-out infinite';
                updateSpeedometer(204);
            }
        },
        torque: {
            effect: () => {
                dynamicCar.style.filter = 'drop-shadow(0 8px 25px rgba(76, 175, 80, 0.8)) brightness(1.15)';
                motionBlur.style.background = 'linear-gradient(90deg, transparent 0%, rgba(76,175,80,0.3) 50%, transparent 100%)';
                motionBlur.style.animation = 'torqueWave 2.5s ease-in-out infinite';
                updateSpeedometer(310);
            }
        }
    };

    const driveModesData = {
        eco: {
            color: '#4CAF50',
            intensity: 0.7
        },
        comfort: {
            color: '#2196F3',
            intensity: 1.0
        },
        sport: {
            color: '#FF5722',
            intensity: 1.3
        }
    };

    function updateSpeedometer(value) {
        const speedValue = speedometer.querySelector('.speed-value');
        const speedNeedle = speedometer.querySelector('.speed-needle');

        speedValue.textContent = value;

        // Animate needle rotation
        const rotation = (value / 400) * 180; // Max 180 degrees
        speedNeedle.style.transform = `rotate(${rotation}deg)`;
    }

    perfStats.forEach(stat => {
        stat.addEventListener('click', function() {
            const statType = this.dataset.stat;

            // Remove active class from all stats
            perfStats.forEach(s => s.classList.remove('active'));

            // Add active class to clicked stat
            this.classList.add('active');

            // Apply performance effect
            if (performanceData[statType]) {
                performanceData[statType].effect();
            }
        });

        stat.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#FF5722';
            }
        });

        stat.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }
        });
    });

    driveModes.forEach(mode => {
        mode.addEventListener('click', function() {
            const modeType = this.dataset.mode;

            // Remove active class from all modes
            driveModes.forEach(m => m.classList.remove('active'));

            // Add active class to clicked mode
            this.classList.add('active');

            // Apply drive mode effect
            if (driveModesData[modeType]) {
                const modeData = driveModesData[modeType];
                dynamicCar.style.filter = `drop-shadow(0 8px 25px ${modeData.color}80) brightness(${modeData.intensity})`;
                this.style.backgroundColor = modeData.color;
            }
        });
    });

    // Auto-cycle through performance stats
    let currentPerfIndex = 0;
    setInterval(() => {
        currentPerfIndex = (currentPerfIndex + 1) % perfStats.length;
        perfStats[currentPerfIndex].click();
    }, 3500);

    // Auto-cycle through drive modes
    let currentModeIndex = 0;
    setInterval(() => {
        currentModeIndex = (currentModeIndex + 1) % driveModes.length;
        driveModes[currentModeIndex].click();
    }, 7000);
}

// Banner 2: Model Showcase
function initModelShowcaseBanner() {
    const modelTabs = document.querySelectorAll('.model-tab');
    const modelName = document.getElementById('model-name');
    const modelFeatures = document.getElementById('model-features');
    const modelPrice = document.getElementById('model-price');
    const carImage = document.getElementById('showcase-car-image');
    
    const models = {
        octavia: {
            name: "Yeni Octavia",
            features: ["LED Matrix", "DSG Şanzıman", "Virtual Cockpit"],
            price: "₺899.000'den başlayan fiyatlarla",
            image: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 350 180\"><path d=\"M50 120 Q70 90 100 95 L250 95 Q280 90 300 120 L330 120 Q340 130 330 145 L300 155 Q280 165 250 155 L100 155 Q70 165 50 155 L20 145 Q10 130 20 120 Z\" fill=\"%2300A651\"/><circle cx=\"100\" cy=\"140\" r=\"20\" fill=\"%23333\"/><circle cx=\"250\" cy=\"140\" r=\"20\" fill=\"%23333\"/><rect x=\"120\" y=\"100\" width=\"110\" height=\"30\" rx=\"5\" fill=\"%23fff\" opacity=\"0.3\"/></svg>')"
        },
        superb: {
            name: "Yeni Superb",
            features: ["Hibrit Motor", "Matrix LED", "Canton Ses"],
            price: "₺1.299.000'den başlayan fiyatlarla",
            image: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 350 180\"><path d=\"M40 115 Q65 85 95 90 L255 90 Q285 85 310 115 L340 115 Q350 125 340 140 L310 150 Q285 160 255 150 L95 150 Q65 160 40 150 L10 140 Q0 125 10 115 Z\" fill=\"%23333\"/><circle cx=\"95\" cy=\"135\" r=\"22\" fill=\"%23222\"/><circle cx=\"255\" cy=\"135\" r=\"22\" fill=\"%23222\"/><rect x=\"115\" y=\"95\" width=\"120\" height=\"35\" rx=\"8\" fill=\"%23fff\" opacity=\"0.4\"/></svg>')"
        },
        kodiaq: {
            name: "Yeni Kodiaq",
            features: ["7 Koltuk", "4x4 Sistem", "Panoramik Tavan"],
            price: "₺1.599.000'den başlayan fiyatlarla",
            image: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 350 180\"><path d=\"M30 110 Q55 75 85 80 L265 80 Q295 75 320 110 L345 110 Q355 120 345 135 L320 145 Q295 155 265 145 L85 145 Q55 155 30 145 L5 135 Q-5 120 5 110 Z\" fill=\"%238B4513\"/><circle cx=\"85\" cy=\"130\" r=\"25\" fill=\"%23444\"/><circle cx=\"265\" cy=\"130\" r=\"25\" fill=\"%23444\"/><rect x=\"105\" y=\"85\" width=\"140\" height=\"40\" rx=\"10\" fill=\"%23fff\" opacity=\"0.5\"/></svg>')"
        },
        enyaq: {
            name: "Enyaq iV",
            features: ["100% Elektrik", "500km Menzil", "Hızlı Şarj"],
            price: "₺1.899.000'den başlayan fiyatlarla",
            image: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 350 180\"><path d=\"M45 112 Q70 82 100 87 L250 87 Q280 82 305 112 L335 112 Q345 122 335 137 L305 147 Q280 157 250 147 L100 147 Q70 157 45 147 L15 137 Q5 122 15 112 Z\" fill=\"%234CAF50\"/><circle cx=\"100\" cy=\"132\" r=\"23\" fill=\"%23555\"/><circle cx=\"250\" cy=\"132\" r=\"23\" fill=\"%23555\"/><rect x=\"120\" y=\"92\" width=\"110\" height=\"35\" rx=\"8\" fill=\"%23fff\" opacity=\"0.6\"/><path d=\"M160 102 L165 92 L170 102 L175 92 L180 102\" stroke=\"%23FFD700\" stroke-width=\"3\" fill=\"none\"/></svg>')"
        }
    };
    
    modelTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const model = this.dataset.model;
            
            // Remove active class from all tabs
            modelTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Update model information
            const modelData = models[model];
            modelName.textContent = modelData.name;
            modelPrice.textContent = modelData.price;
            
            // Update features
            modelFeatures.innerHTML = '';
            modelData.features.forEach(feature => {
                const badge = document.createElement('span');
                badge.className = 'feature-badge';
                badge.textContent = feature;
                modelFeatures.appendChild(badge);
            });
            
            // Update car image with animation
            carImage.style.opacity = '0';
            carImage.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                carImage.style.backgroundImage = modelData.image;
                carImage.style.opacity = '1';
                carImage.style.transform = 'translateY(0)';
            }, 300);
        });
        
        tab.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.borderColor = '#00A651';
                this.style.transform = 'scale(1.05)';
            }
        });
        
        tab.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                this.style.transform = 'scale(1)';
            }
        });
    });
    
    // Auto-cycle through models
    let currentModelIndex = 0;
    setInterval(() => {
        currentModelIndex = (currentModelIndex + 1) % modelTabs.length;
        modelTabs[currentModelIndex].click();
    }, 5000);
}

// Banner 6: Sustainability
function initSustainabilityBanner() {
    const metricCards = document.querySelectorAll('.metric-card');
    const ecoDesc = document.getElementById('eco-desc');
    const ecoEffects = document.getElementById('eco-effects');
    const sustainableCar = document.querySelector('.sustainable-car');

    const metrics = {
        emission: {
            description: "Tamamen elektrikli Elroq ile çevreye saygılı sürüş deneyimi",
            effect: () => {
                sustainableCar.style.filter = 'drop-shadow(0 8px 25px rgba(76, 175, 80, 0.6)) brightness(1.1)';
                ecoEffects.style.animation = 'ecoFloat 3s ease-in-out infinite';
            }
        },
        energy: {
            description: "Düşük enerji tüketimi ile uzun menzil ve ekonomik sürüş",
            effect: () => {
                sustainableCar.style.filter = 'drop-shadow(0 8px 25px rgba(255, 193, 7, 0.6)) brightness(1.15)';
                ecoEffects.style.animation = 'energyPulse 2s ease-in-out infinite';
            }
        },
        recycling: {
            description: "Geri dönüştürülebilir malzemeler ile sürdürülebilir üretim",
            effect: () => {
                sustainableCar.style.filter = 'drop-shadow(0 8px 25px rgba(0, 150, 136, 0.6)) brightness(1.1)';
                ecoEffects.style.animation = 'recycleRotate 4s linear infinite';
            }
        }
    };

    metricCards.forEach(card => {
        card.addEventListener('click', function() {
            const metric = this.dataset.metric;

            // Remove active class from all cards
            metricCards.forEach(c => c.classList.remove('active'));

            // Add active class to clicked card
            this.classList.add('active');

            // Update description and effects
            if (metrics[metric]) {
                ecoDesc.textContent = metrics[metric].description;
                metrics[metric].effect();
            }
        });

        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(1.05)';
                this.style.borderColor = '#A5D6A7';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'scale(0.95)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }
        });
    });

    // Auto-cycle through metrics
    let currentMetricIndex = 0;
    setInterval(() => {
        hasAnimated = false;
    }, 10000);
    
    // Auto-trigger animation
    setTimeout(() => {
        sustainabilityBanner.dispatchEvent(new Event('mouseenter'));
    }, 2000);
}

// Banner 4: Heritage
function initHeritageBanner() {
    const timelinePoints = document.querySelectorAll('.timeline-point');
    const heritageText = document.getElementById('heritage-text');
    const vintagecar = document.querySelector('.vintage-car');
    const modernCar = document.querySelector('.modern-car');
    
    const heritage = {
        1895: {
            text: "Václav Laurin ve Václav Klement tarafından kurulan marka, 129 yıldır kalite ve inovasyonun simgesi.",
            vintage: true
        },
        1925: {
            text: "Škoda Works ile birleşerek otomotiv endüstrisinde güçlü bir oyuncu haline geldi.",
            vintage: true
        },
        1991: {
            text: "Volkswagen Group'a katılarak modern teknoloji ve kalite standartlarını benimsedi.",
            vintage: false
        },
        2024: {
            text: "Elektrikli gelecekte öncü, Simply Clever felsefesiyle sürdürülebilir mobilite sunuyor.",
            vintage: false
        }
    };
    
    timelinePoints.forEach(point => {
        point.addEventListener('click', function() {
            const year = this.dataset.year;
            
            // Remove active class from all points
            timelinePoints.forEach(p => p.classList.remove('active'));
            
            // Add active class to clicked point
            this.classList.add('active');
            
            // Update heritage text
            heritageText.textContent = heritage[year].text;
            
            // Show/hide cars based on era
            if (heritage[year].vintage) {
                vintagecar.style.opacity = '1';
                vintagecar.style.transform = 'scale(1.1)';
                modernCar.style.opacity = '0.3';
                modernCar.style.transform = 'scale(0.9)';
            } else {
                vintagecar.style.opacity = '0.3';
                vintagecar.style.transform = 'scale(0.9)';
                modernCar.style.opacity = '1';
                modernCar.style.transform = 'scale(1.1)';
            }
        });
        
        point.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.borderColor = '#FFD700';
                this.style.transform = 'scale(1.05)';
            }
        });
        
        point.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                this.style.transform = 'scale(1)';
            }
        });
    });
    
    // Auto-cycle through timeline
    let currentTimelineIndex = 0;
    setInterval(() => {
        currentTimelineIndex = (currentTimelineIndex + 1) % timelinePoints.length;
        timelinePoints[currentTimelineIndex].click();
    }, 6000);
}

// Global banner interactions
document.querySelectorAll('.banner').forEach(banner => {
    banner.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px)';
        this.style.boxShadow = '0 16px 48px rgba(0, 0, 0, 0.2)';
    });
    
    banner.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.15)';
    });
    
    banner.addEventListener('click', function() {
        // Add click animation
        this.style.transform = 'translateY(0px) scale(0.98)';
        setTimeout(() => {
            this.style.transform = 'translateY(-2px) scale(1)';
        }, 150);
    });
});

// Add some interactive particles for visual appeal
function createParticle(container, color = '#00A651') {
    const particle = document.createElement('div');
    particle.style.position = 'absolute';
    particle.style.width = '4px';
    particle.style.height = '4px';
    particle.style.backgroundColor = color;
    particle.style.borderRadius = '50%';
    particle.style.pointerEvents = 'none';
    particle.style.opacity = '0.7';
    
    const x = Math.random() * container.offsetWidth;
    const y = Math.random() * container.offsetHeight;
    
    particle.style.left = x + 'px';
    particle.style.top = y + 'px';
    
    container.appendChild(particle);
    
    // Animate particle
    const animation = particle.animate([
        { transform: 'translate(0, 0) scale(0)', opacity: 0 },
        { transform: 'translate(0, -20px) scale(1)', opacity: 0.7 },
        { transform: 'translate(0, -40px) scale(0)', opacity: 0 }
    ], {
        duration: 2000,
        easing: 'ease-out'
    });
    
    animation.onfinish = () => {
        particle.remove();
    };
}

// Add particles on banner hover
document.querySelectorAll('.banner').forEach(banner => {
    banner.addEventListener('mouseenter', function() {
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                createParticle(this);
            }, i * 200);
        }
    });
});

console.log('🚗 Skoda Interactive Banners loaded successfully!');
